/**
 * 数据适配器 - 处理现有数据结构与 hiprint 之间的转换
 * @since 2025-01-27
 */

/**
 * 将现有的 labelZones 数据转换为 hiprint 模板格式
 * @param {Array} labelZones - 现有的区域数据
 * @param {Object} canvasConfig - 画布配置
 * @returns {Object} hiprint 模板数据
 */
export function convertLabelZonesToHiprint(labelZones, canvasConfig = {}) {
  const template = {
    panels: [
      {
        index: 0,
        height: canvasConfig.height || 26, // mm
        width: canvasConfig.width || 192,  // mm
        paperHeader: 0,
        paperFooter: 0,
        printElements: []
      }
    ]
  }

  // 转换每个区域为 hiprint 元素
  labelZones.forEach((zone, index) => {
    const hiprintElement = convertZoneToHiprintElement(zone, index)
    if (hiprintElement) {
      template.panels[0].printElements.push(hiprintElement)
    }
  })

  return template
}

/**
 * 将单个区域转换为 hiprint 元素
 * @param {Object} zone - 区域数据
 * @param {number} index - 元素索引
 * @returns {Object} hiprint 元素
 */
function convertZoneToHiprintElement(zone, index) {
  const baseElement = {
    options: {
      left: mmToPt(zone.x || 0),
      top: mmToPt(zone.y || 0),
      height: mmToPt(zone.height || 20),
      width: mmToPt(zone.width || 50),
      fontSize: zone.fontSize || 12,
      color: zone.color || '#333333',
      backgroundColor: zone.backgroundColor || 'transparent',
      fontWeight: zone.fontWeight || 'normal',
      textAlign: zone.textAlign || 'left',
      lineHeight: zone.lineHeight || 1.2,
      zIndex: index + 1
    }
  }

  // 根据区域类型和字段确定元素类型
  if (zone.fields && zone.fields.length > 0) {
    // 包含字段的文本元素
    return {
      ...baseElement,
      type: 'text',
      options: {
        ...baseElement.options,
        title: generateFieldText(zone.fields, zone.layout),
        field: zone.fields[0], // 主要字段
        testData: generateTestData(zone.fields)
      }
    }
  } else if (zone.id === 'logo') {
    // Logo 图片元素
    return {
      ...baseElement,
      type: 'image',
      options: {
        ...baseElement.options,
        src: '', // 空的图片源
        fit: 'contain'
      }
    }
  } else {
    // 普通文本元素
    return {
      ...baseElement,
      type: 'text',
      options: {
        ...baseElement.options,
        title: zone.name || '文本'
      }
    }
  }
}

/**
 * 将 hiprint 模板转换为现有的 labelZones 格式
 * @param {Object} hiprintTemplate - hiprint 模板数据
 * @returns {Array} labelZones 数据
 */
export function convertHiprintToLabelZones(hiprintTemplate) {
  const labelZones = []

  if (hiprintTemplate.panels && hiprintTemplate.panels[0]) {
    const panel = hiprintTemplate.panels[0]
    
    panel.printElements.forEach((element, index) => {
      const zone = convertHiprintElementToZone(element, index)
      if (zone) {
        labelZones.push(zone)
      }
    })
  }

  return labelZones
}

/**
 * 将 hiprint 元素转换为区域数据
 * @param {Object} element - hiprint 元素
 * @param {number} index - 元素索引
 * @returns {Object} 区域数据
 */
function convertHiprintElementToZone(element, index) {
  const options = element.options || {}
  
  const zone = {
    id: `zone_${index}`,
    name: options.title || `区域${index + 1}`,
    x: ptToMm(options.left || 0),
    y: ptToMm(options.top || 0),
    width: ptToMm(options.width || 50),
    height: ptToMm(options.height || 20),
    fontSize: options.fontSize || 12,
    color: options.color || '#333333',
    backgroundColor: options.backgroundColor || 'transparent',
    fontWeight: options.fontWeight || 'normal',
    textAlign: options.textAlign || 'left',
    lineHeight: options.lineHeight || 1.2,
    fields: []
  }

  // 提取字段信息
  if (options.field) {
    zone.fields = [options.field]
  }

  // 根据元素类型设置特殊属性
  if (element.type === 'image') {
    zone.id = 'logo'
    zone.name = '公司Logo位置(预留)'
    zone.fields = []
  }

  return zone
}

/**
 * 生成字段组合文本
 * @param {Array} fields - 字段列表
 * @param {string} layout - 布局方式
 * @returns {string} 组合文本
 */
function generateFieldText(fields, layout = 'vertical') {
  if (!fields || fields.length === 0) return ''

  const fieldDisplayNames = {
    bankName: '银行名称',
    coinName1: '钱币名称1',
    coinName2: '钱币名称2',
    serialNumber: '编号',
    version: '版别',
    gradeScore: '评级分数',
    gradeLevel: '评级等级',
    specialMark: '特殊标记',
    diyCode: '送评条码',
    yearInfo: '年代信息',
    faceValue: '面值',
    coinType: '钱币类型'
  }

  if (layout === 'vertical') {
    return fields.map(field => fieldDisplayNames[field] || field).join('\n')
  } else {
    return fields.map(field => fieldDisplayNames[field] || field).join(' ')
  }
}

/**
 * 生成测试数据
 * @param {Array} fields - 字段列表
 * @returns {Object} 测试数据
 */
function generateTestData(fields) {
  const testData = {}
  
  const sampleData = {
    bankName: '中国人民银行',
    coinName1: '第四套人民币',
    coinName2: '1980年版',
    serialNumber: 'AB12345678',
    version: '普通版',
    gradeScore: '65',
    gradeLevel: 'EPQ',
    specialMark: '★',
    diyCode: 'DY2025001',
    yearInfo: '1980年',
    faceValue: '10元',
    coinType: '纸币'
  }

  fields.forEach(field => {
    testData[field] = sampleData[field] || field
  })

  return testData
}

/**
 * 毫米转磅值 (1mm ≈ 2.*********pt)
 * @param {number} mm - 毫米值
 * @returns {number} 磅值
 */
function mmToPt(mm) {
  return Math.round(mm * 2.*********)
}

/**
 * 磅值转毫米 (1pt ≈ 0.*********mm)
 * @param {number} pt - 磅值
 * @returns {number} 毫米值
 */
function ptToMm(pt) {
  return Math.round(pt * 0.********* * 100) / 100
}

/**
 * 创建默认的大签模板
 * @returns {Object} hiprint 模板数据
 */
export function createDefaultLargeTemplate() {
  return {
    panels: [
      {
        index: 0,
        height: 26,
        width: 192,
        paperHeader: 0,
        paperFooter: 0,
        printElements: [
          {
            type: 'image',
            options: {
              left: 0,
              top: 0,
              height: mmToPt(26),
              width: mmToPt(30),
              src: '',
              fit: 'contain',
              title: '公司Logo'
            }
          },
          {
            type: 'text',
            options: {
              left: mmToPt(30),
              top: 0,
              height: mmToPt(26),
              width: mmToPt(100),
              fontSize: 10,
              color: '#333333',
              title: '中国人民银行\n第四套人民币\nAB12345678',
              field: 'bankName',
              textAlign: 'left',
              lineHeight: 1.2
            }
          },
          {
            type: 'text',
            options: {
              left: mmToPt(130),
              top: 0,
              height: mmToPt(26),
              width: mmToPt(30),
              fontSize: 12,
              color: '#ff0000',
              title: '★',
              field: 'specialMark',
              textAlign: 'center'
            }
          },
          {
            type: 'text',
            options: {
              left: mmToPt(160),
              top: 0,
              height: mmToPt(26),
              width: mmToPt(32),
              fontSize: 14,
              color: '#333333',
              title: '65\nEPQ',
              field: 'gradeScore',
              textAlign: 'center',
              lineHeight: 1.0
            }
          }
        ]
      }
    ]
  }
}

/**
 * 创建默认的小签模板
 * @returns {Object} hiprint 模板数据
 */
export function createDefaultSmallTemplate() {
  return {
    panels: [
      {
        index: 0,
        height: 21,
        width: 115,
        paperHeader: 0,
        paperFooter: 0,
        printElements: [
          {
            type: 'image',
            options: {
              left: 0,
              top: 0,
              height: mmToPt(21),
              width: mmToPt(20),
              src: '',
              fit: 'contain',
              title: '公司Logo'
            }
          },
          {
            type: 'text',
            options: {
              left: mmToPt(20),
              top: 0,
              height: mmToPt(21),
              width: mmToPt(60),
              fontSize: 9,
              color: '#333333',
              title: '中国人民银行\n第四套人民币\nAB12345678',
              field: 'bankName',
              textAlign: 'left',
              lineHeight: 1.1
            }
          },
          {
            type: 'text',
            options: {
              left: mmToPt(80),
              top: 0,
              height: mmToPt(21),
              width: mmToPt(20),
              fontSize: 10,
              color: '#ff0000',
              title: '★',
              field: 'specialMark',
              textAlign: 'center'
            }
          },
          {
            type: 'text',
            options: {
              left: mmToPt(100),
              top: 0,
              height: mmToPt(21),
              width: mmToPt(15),
              fontSize: 12,
              color: '#333333',
              title: '65\nEPQ',
              field: 'gradeScore',
              textAlign: 'center',
              lineHeight: 1.0
            }
          }
        ]
      }
    ]
  }
}

/**
 * 字段映射配置
 */
export const FIELD_MAPPING = {
  // 基础信息字段
  BASIC_INFO: [
    { fieldName: 'bankName', displayName: '银行名称', category: 'BASIC_INFO' },
    { fieldName: 'coinName1', displayName: '钱币名称1', category: 'BASIC_INFO' },
    { fieldName: 'coinName2', displayName: '钱币名称2', category: 'BASIC_INFO' },
    { fieldName: 'serialNumber', displayName: '编号', category: 'BASIC_INFO' },
    { fieldName: 'version', displayName: '版别', category: 'BASIC_INFO' },
    { fieldName: 'yearInfo', displayName: '年代信息', category: 'BASIC_INFO' },
    { fieldName: 'faceValue', displayName: '面值', category: 'BASIC_INFO' },
    { fieldName: 'coinType', displayName: '钱币类型', category: 'BASIC_INFO' },
    { fieldName: 'diyCode', displayName: '送评条码', category: 'BASIC_INFO' }
  ],
  
  // 评级信息字段
  GRADE_INFO: [
    { fieldName: 'gradeScore', displayName: '评级分数', category: 'GRADE_INFO' },
    { fieldName: 'gradeLevel', displayName: '评级等级', category: 'GRADE_INFO' },
    { fieldName: 'specialMark', displayName: '特殊标记', category: 'GRADE_INFO' },
    { fieldName: 'scoreRemarks', displayName: '评分备注', category: 'GRADE_INFO' }
  ],
  
  // 特殊字段
  SPECIAL: [
    { fieldName: 'qrCode', displayName: '二维码', category: 'SPECIAL' },
    { fieldName: 'barCode', displayName: '条形码', category: 'SPECIAL' }
  ]
}

/**
 * 获取字段显示名称
 * @param {string} fieldName - 字段名
 * @returns {string} 显示名称
 */
export function getFieldDisplayName(fieldName) {
  const allFields = [
    ...FIELD_MAPPING.BASIC_INFO,
    ...FIELD_MAPPING.GRADE_INFO,
    ...FIELD_MAPPING.SPECIAL
  ]
  
  const field = allFields.find(f => f.fieldName === fieldName)
  return field ? field.displayName : fieldName
}

/**
 * 验证模板数据
 * @param {Object} templateData - 模板数据
 * @returns {Object} 验证结果
 */
export function validateTemplateData(templateData) {
  const result = {
    valid: true,
    errors: []
  }

  if (!templateData) {
    result.valid = false
    result.errors.push('模板数据不能为空')
    return result
  }

  if (!templateData.panels || !Array.isArray(templateData.panels)) {
    result.valid = false
    result.errors.push('模板必须包含面板数据')
    return result
  }

  if (templateData.panels.length === 0) {
    result.valid = false
    result.errors.push('模板至少需要一个面板')
    return result
  }

  const panel = templateData.panels[0]
  if (!panel.printElements || !Array.isArray(panel.printElements)) {
    result.valid = false
    result.errors.push('面板必须包含打印元素')
    return result
  }

  return result
}
