/**
 * 迁移工具 - 帮助用户从旧版设计器迁移到新版
 * @since 2025-01-27
 */

import { convertLabelZonesToHiprint, validateTemplateData } from './dataAdapter'

/**
 * 迁移旧版模板数据到新版格式
 * @param {Object} legacyTemplate - 旧版模板数据
 * @returns {Object} 迁移结果
 */
export function migrateLegacyTemplate(legacyTemplate) {
  const result = {
    success: false,
    data: null,
    errors: [],
    warnings: []
  }

  try {
    // 验证输入数据
    if (!legacyTemplate) {
      result.errors.push('模板数据不能为空')
      return result
    }

    // 解析旧版布局配置
    let layoutConfig = null
    if (typeof legacyTemplate.layoutConfig === 'string') {
      try {
        layoutConfig = JSON.parse(legacyTemplate.layoutConfig)
      } catch (error) {
        result.errors.push('布局配置格式错误')
        return result
      }
    } else {
      layoutConfig = legacyTemplate.layoutConfig
    }

    if (!layoutConfig || !layoutConfig.zones) {
      result.errors.push('缺少区域配置数据')
      return result
    }

    // 解析字段映射
    let fieldMapping = {}
    if (legacyTemplate.fieldMapping) {
      try {
        fieldMapping = typeof legacyTemplate.fieldMapping === 'string'
          ? JSON.parse(legacyTemplate.fieldMapping)
          : legacyTemplate.fieldMapping
      } catch (error) {
        result.warnings.push('字段映射解析失败，将使用默认配置')
      }
    }

    // 转换画布配置
    const canvasConfig = layoutConfig.canvas || {
      width: 192,
      height: 26
    }

    // 转换区域数据
    const labelZones = layoutConfig.zones || []
    
    // 应用字段映射到区域
    labelZones.forEach(zone => {
      if (fieldMapping[zone.id]) {
        zone.fields = fieldMapping[zone.id]
      }
    })

    // 转换为 hiprint 格式
    const hiprintTemplate = convertLabelZonesToHiprint(labelZones, canvasConfig)

    // 验证转换结果
    const validation = validateTemplateData(hiprintTemplate)
    if (!validation.valid) {
      result.errors.push(...validation.errors)
      return result
    }

    // 构建新版模板数据
    const newTemplate = {
      id: legacyTemplate.id,
      templateName: legacyTemplate.templateName || '迁移模板',
      templateType: legacyTemplate.templateType || 'CUSTOM',
      layoutConfig: JSON.stringify(hiprintTemplate),
      fieldMapping: JSON.stringify(extractFieldMapping(hiprintTemplate)),
      createTime: legacyTemplate.createTime,
      updateTime: new Date().toISOString(),
      migrated: true,
      originalVersion: 'legacy'
    }

    result.success = true
    result.data = newTemplate

    // 添加迁移统计信息
    result.stats = {
      originalZones: labelZones.length,
      convertedElements: hiprintTemplate.panels[0]?.printElements?.length || 0,
      fieldsCount: Object.keys(fieldMapping).length
    }

    // 检查可能的问题并添加警告
    checkMigrationIssues(labelZones, result.warnings)

  } catch (error) {
    result.errors.push(`迁移失败: ${error.message}`)
  }

  return result
}

/**
 * 批量迁移多个模板
 * @param {Array} legacyTemplates - 旧版模板列表
 * @returns {Object} 批量迁移结果
 */
export function batchMigrateLegacyTemplates(legacyTemplates) {
  const result = {
    total: legacyTemplates.length,
    success: 0,
    failed: 0,
    results: [],
    summary: {
      errors: [],
      warnings: []
    }
  }

  legacyTemplates.forEach((template, index) => {
    const migrationResult = migrateLegacyTemplate(template)
    
    if (migrationResult.success) {
      result.success++
    } else {
      result.failed++
      result.summary.errors.push({
        templateName: template.templateName || `模板${index + 1}`,
        errors: migrationResult.errors
      })
    }

    result.results.push({
      index,
      templateName: template.templateName || `模板${index + 1}`,
      ...migrationResult
    })

    // 收集警告
    if (migrationResult.warnings && migrationResult.warnings.length > 0) {
      result.summary.warnings.push({
        templateName: template.templateName || `模板${index + 1}`,
        warnings: migrationResult.warnings
      })
    }
  })

  return result
}

/**
 * 从 hiprint 模板中提取字段映射
 * @param {Object} hiprintTemplate - hiprint 模板数据
 * @returns {Object} 字段映射
 */
function extractFieldMapping(hiprintTemplate) {
  const fieldMapping = {}

  if (hiprintTemplate.panels) {
    hiprintTemplate.panels.forEach(panel => {
      if (panel.printElements) {
        panel.printElements.forEach((element, index) => {
          if (element.options && element.options.field) {
            const zoneId = `zone_${index}`
            fieldMapping[zoneId] = [element.options.field]
          }
        })
      }
    })
  }

  return fieldMapping
}

/**
 * 检查迁移过程中可能的问题
 * @param {Array} labelZones - 原始区域数据
 * @param {Array} warnings - 警告数组
 */
function checkMigrationIssues(labelZones, warnings) {
  // 检查是否有复杂的布局
  const complexLayouts = labelZones.filter(zone => 
    zone.layout === 'complex' || 
    (zone.fields && zone.fields.length > 3)
  )
  
  if (complexLayouts.length > 0) {
    warnings.push('检测到复杂布局，可能需要手动调整')
  }

  // 检查是否有自定义样式
  const customStyles = labelZones.filter(zone => 
    zone.borderRadius || 
    zone.borderWidth || 
    zone.customCss
  )
  
  if (customStyles.length > 0) {
    warnings.push('检测到自定义样式，请检查转换后的效果')
  }

  // 检查是否有特殊字段类型
  const specialFields = labelZones.filter(zone => 
    zone.fields && zone.fields.some(field => 
      field.includes('qrCode') || 
      field.includes('barCode') || 
      field.includes('image')
    )
  )
  
  if (specialFields.length > 0) {
    warnings.push('检测到特殊字段类型，请验证功能是否正常')
  }

  // 检查区域重叠
  const overlappingZones = findOverlappingZones(labelZones)
  if (overlappingZones.length > 0) {
    warnings.push('检测到区域重叠，可能影响显示效果')
  }
}

/**
 * 查找重叠的区域
 * @param {Array} zones - 区域列表
 * @returns {Array} 重叠的区域对
 */
function findOverlappingZones(zones) {
  const overlapping = []
  
  for (let i = 0; i < zones.length; i++) {
    for (let j = i + 1; j < zones.length; j++) {
      if (isZoneOverlapping(zones[i], zones[j])) {
        overlapping.push([zones[i], zones[j]])
      }
    }
  }
  
  return overlapping
}

/**
 * 检查两个区域是否重叠
 * @param {Object} zone1 - 区域1
 * @param {Object} zone2 - 区域2
 * @returns {boolean} 是否重叠
 */
function isZoneOverlapping(zone1, zone2) {
  const x1 = zone1.x || 0
  const y1 = zone1.y || 0
  const w1 = zone1.width || 0
  const h1 = zone1.height || 0
  
  const x2 = zone2.x || 0
  const y2 = zone2.y || 0
  const w2 = zone2.width || 0
  const h2 = zone2.height || 0
  
  return !(x1 + w1 <= x2 || x2 + w2 <= x1 || y1 + h1 <= y2 || y2 + h2 <= y1)
}

/**
 * 生成迁移报告
 * @param {Object} migrationResult - 迁移结果
 * @returns {string} 报告文本
 */
export function generateMigrationReport(migrationResult) {
  let report = '# 模板迁移报告\n\n'
  
  if (migrationResult.total) {
    // 批量迁移报告
    report += `## 迁移概览\n`
    report += `- 总数: ${migrationResult.total}\n`
    report += `- 成功: ${migrationResult.success}\n`
    report += `- 失败: ${migrationResult.failed}\n`
    report += `- 成功率: ${Math.round((migrationResult.success / migrationResult.total) * 100)}%\n\n`
    
    if (migrationResult.summary.errors.length > 0) {
      report += `## 错误详情\n`
      migrationResult.summary.errors.forEach(error => {
        report += `### ${error.templateName}\n`
        error.errors.forEach(err => {
          report += `- ${err}\n`
        })
        report += '\n'
      })
    }
    
    if (migrationResult.summary.warnings.length > 0) {
      report += `## 警告信息\n`
      migrationResult.summary.warnings.forEach(warning => {
        report += `### ${warning.templateName}\n`
        warning.warnings.forEach(warn => {
          report += `- ${warn}\n`
        })
        report += '\n'
      })
    }
  } else {
    // 单个模板迁移报告
    report += `## 迁移状态: ${migrationResult.success ? '成功' : '失败'}\n\n`
    
    if (migrationResult.stats) {
      report += `## 迁移统计\n`
      report += `- 原始区域数: ${migrationResult.stats.originalZones}\n`
      report += `- 转换元素数: ${migrationResult.stats.convertedElements}\n`
      report += `- 字段数量: ${migrationResult.stats.fieldsCount}\n\n`
    }
    
    if (migrationResult.errors && migrationResult.errors.length > 0) {
      report += `## 错误信息\n`
      migrationResult.errors.forEach(error => {
        report += `- ${error}\n`
      })
      report += '\n'
    }
    
    if (migrationResult.warnings && migrationResult.warnings.length > 0) {
      report += `## 警告信息\n`
      migrationResult.warnings.forEach(warning => {
        report += `- ${warning}\n`
      })
      report += '\n'
    }
  }
  
  report += `## 建议\n`
  report += `1. 迁移完成后，请仔细检查模板的显示效果\n`
  report += `2. 测试打印功能确保输出正确\n`
  report += `3. 如有问题，可以保留原版模板作为备份\n`
  report += `4. 建议在正式使用前进行充分测试\n\n`
  
  report += `---\n`
  report += `迁移时间: ${new Date().toLocaleString('zh-CN')}\n`
  
  return report
}

/**
 * 验证迁移后的模板
 * @param {Object} newTemplate - 新版模板
 * @param {Object} originalTemplate - 原版模板
 * @returns {Object} 验证结果
 */
export function validateMigratedTemplate(newTemplate, originalTemplate) {
  const result = {
    valid: true,
    issues: [],
    suggestions: []
  }

  try {
    // 解析新模板数据
    const layoutConfig = JSON.parse(newTemplate.layoutConfig)
    const validation = validateTemplateData(layoutConfig)
    
    if (!validation.valid) {
      result.valid = false
      result.issues.push(...validation.errors)
    }

    // 比较元素数量
    const originalZones = JSON.parse(originalTemplate.layoutConfig || '{}').zones || []
    const newElements = layoutConfig.panels?.[0]?.printElements || []
    
    if (originalZones.length !== newElements.length) {
      result.suggestions.push(`元素数量发生变化: ${originalZones.length} -> ${newElements.length}`)
    }

    // 检查字段映射
    const originalMapping = JSON.parse(originalTemplate.fieldMapping || '{}')
    const newMapping = JSON.parse(newTemplate.fieldMapping || '{}')
    
    const originalFields = Object.values(originalMapping).flat()
    const newFields = Object.values(newMapping).flat()
    
    if (originalFields.length !== newFields.length) {
      result.suggestions.push(`字段数量发生变化: ${originalFields.length} -> ${newFields.length}`)
    }

  } catch (error) {
    result.valid = false
    result.issues.push(`验证失败: ${error.message}`)
  }

  return result
}

/**
 * 创建迁移备份
 * @param {Object} originalTemplate - 原始模板
 * @returns {Object} 备份数据
 */
export function createMigrationBackup(originalTemplate) {
  return {
    ...originalTemplate,
    backupTime: new Date().toISOString(),
    backupVersion: 'legacy',
    isBackup: true
  }
}
