<template>
  <div class="label-designer-container">
    <!-- 新版 hiprint 设计器 -->
    <HiprintDesigner
      v-if="useNewDesigner"
      @template-saved="handleTemplateSaved"
      @template-loaded="handleTemplateLoaded"
    />

    <!-- 迁移向导 -->
    <MigrationWizard
      v-model:visible="showMigrationWizard"
      @migration-completed="handleMigrationCompleted"
    />
    
    <!-- 原版设计器（保留作为备用） -->
    <div v-else class="legacy-designer">
      <!-- 这里可以保留原有的设计器代码作为备用 -->
      <el-alert
        title="当前使用原版设计器"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <p>您正在使用原版标签设计器。</p>
          <el-button type="primary" size="small" @click="switchToNewDesigner">
            切换到新版设计器
          </el-button>
        </template>
      </el-alert>
    </div>

    <!-- 版本切换控制 -->
    <div class="version-switch" v-if="showVersionSwitch">
      <el-card shadow="hover" style="width: 300px;">
        <template #header>
          <div class="card-header">
            <span>设计器版本选择</span>
          </div>
        </template>
        <div class="version-options">
          <el-radio-group v-model="designerVersion" @change="handleVersionChange">
            <el-radio label="new">
              <div class="version-option">
                <strong>新版设计器 (推荐)</strong>
                <p>基于 vue-plugin-hiprint，功能更强大</p>
                <ul>
                  <li>✅ 可视化拖拽设计</li>
                  <li>✅ 丰富的样式配置</li>
                  <li>✅ 专业的打印引擎</li>
                  <li>✅ 更好的用户体验</li>
                  <li>✅ 支持批量打印</li>
                  <li>✅ 模板迁移工具</li>
                </ul>
              </div>
            </el-radio>
            <el-radio label="legacy">
              <div class="version-option">
                <strong>原版设计器</strong>
                <p>现有的自定义实现</p>
                <ul>
                  <li>⚠️ 维护复杂</li>
                  <li>⚠️ 功能有限</li>
                  <li>⚠️ 样式配置分散</li>
                </ul>
              </div>
            </el-radio>
          </el-radio-group>
        </div>
        <div class="version-actions">
          <el-button @click="showVersionSwitch = false">取消</el-button>
          <el-button @click="showMigrationWizard = true">模板迁移</el-button>
          <el-button type="primary" @click="confirmVersionChange">确认切换</el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import HiprintDesigner from './components/HiprintDesigner.vue'
import MigrationWizard from './components/MigrationWizard.vue'

// 响应式数据
const designerVersion = ref('new') // 'new' | 'legacy'
const showVersionSwitch = ref(false)
const showMigrationWizard = ref(false)

// 计算属性
const useNewDesigner = computed(() => designerVersion.value === 'new')

// 事件处理
const handleTemplateSaved = (templateData) => {
  console.log('模板已保存:', templateData)
  ElMessage.success('模板保存成功')
}

const handleTemplateLoaded = (templateData) => {
  console.log('模板已加载:', templateData)
  ElMessage.success('模板加载成功')
}

const switchToNewDesigner = () => {
  designerVersion.value = 'new'
  ElMessage.success('已切换到新版设计器')
}

const handleVersionChange = (version) => {
  console.log('版本切换:', version)
}

const confirmVersionChange = async () => {
  try {
    if (designerVersion.value === 'legacy') {
      await ElMessageBox.confirm(
        '切换到原版设计器可能会丢失新版设计器的高级功能，确定要切换吗？',
        '确认切换',
        {
          type: 'warning',
          confirmButtonText: '确定切换',
          cancelButtonText: '取消'
        }
      )
    }
    
    showVersionSwitch.value = false
    
    // 保存用户选择到本地存储
    localStorage.setItem('labelDesignerVersion', designerVersion.value)
    
    ElMessage.success(`已切换到${designerVersion.value === 'new' ? '新版' : '原版'}设计器`)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('切换版本失败:', error)
    }
  }
}

const handleMigrationCompleted = () => {
  ElMessage.success('模板迁移完成')
  showMigrationWizard.value = false
  // 刷新模板列表或其他必要操作
}

// 生命周期
onMounted(() => {
  // 从本地存储读取用户偏好
  const savedVersion = localStorage.getItem('labelDesignerVersion')
  if (savedVersion && ['new', 'legacy'].includes(savedVersion)) {
    designerVersion.value = savedVersion
  }
  
  // 如果是首次访问，显示版本选择
  const hasShownVersionChoice = localStorage.getItem('hasShownLabelDesignerVersionChoice')
  if (!hasShownVersionChoice) {
    showVersionSwitch.value = true
    localStorage.setItem('hasShownLabelDesignerVersionChoice', 'true')
  }
})

// 暴露方法给父组件
defineExpose({
  switchToNewDesigner,
  showVersionSwitch: () => {
    showVersionSwitch.value = true
  }
})
</script>

<style scoped>
.label-designer-container {
  height: 100vh;
  position: relative;
}

.legacy-designer {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}

.version-switch {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.version-options {
  margin: 16px 0;
}

.version-option {
  margin-left: 24px;
  margin-top: 8px;
}

.version-option p {
  margin: 4px 0;
  color: #666;
  font-size: 14px;
}

.version-option ul {
  margin: 8px 0;
  padding-left: 16px;
}

.version-option li {
  margin: 4px 0;
  font-size: 13px;
  color: #666;
}

.version-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}

:deep(.el-radio) {
  display: block;
  margin-bottom: 16px;
  align-items: flex-start;
}

:deep(.el-radio__label) {
  display: block;
  white-space: normal;
}
</style>
