/**
 * 银行券标签设计器路由配置
 * @since 2025-01-27
 */

/**
 * 标签设计器相关路由
 */
export const labelDesignerRoutes = [
  {
    path: '/bank-note/label-designer',
    name: 'BankNoteLabelDesigner',
    component: () => import('./index-new.vue'),
    meta: {
      title: '标签设计器',
      icon: 'DesktopOutlined',
      authority: 'banknote:label:design',
      keepAlive: false,
      hideFooter: true
    }
  },
  {
    path: '/bank-note/label-designer/legacy',
    name: 'BankNoteLabelDesignerLegacy',
    component: () => import('./index.vue'),
    meta: {
      title: '标签设计器(旧版)',
      icon: 'DesktopOutlined',
      authority: 'banknote:label:design',
      keepAlive: false,
      hideFooter: true,
      hidden: true // 隐藏菜单项，仅作为备用路由
    }
  },
  {
    path: '/bank-note/label-designer/preview/:templateId',
    name: 'BankNoteLabelPreview',
    component: () => import('./components/TemplatePreview.vue'),
    meta: {
      title: '模板预览',
      authority: 'banknote:label:view',
      keepAlive: false,
      hideFooter: true,
      hidden: true
    }
  },
  {
    path: '/bank-note/label-designer/print/:templateId',
    name: 'BankNoteLabelPrint',
    component: () => import('./components/PrintPage.vue'),
    meta: {
      title: '标签打印',
      authority: 'banknote:label:print',
      keepAlive: false,
      hideFooter: true,
      hidden: true
    }
  }
]

/**
 * 动态路由注册函数
 * 根据用户权限和配置动态注册路由
 */
export function registerLabelDesignerRoutes(router, userPermissions = [], config = {}) {
  const {
    enableNewDesigner = true,
    enableLegacyDesigner = true,
    defaultDesigner = 'new'
  } = config

  // 检查用户权限
  const hasDesignPermission = userPermissions.includes('banknote:label:design')
  const hasViewPermission = userPermissions.includes('banknote:label:view')
  const hasPrintPermission = userPermissions.includes('banknote:label:print')

  if (!hasDesignPermission && !hasViewPermission && !hasPrintPermission) {
    console.warn('用户没有标签设计器相关权限')
    return
  }

  // 注册主设计器路由
  if (enableNewDesigner && hasDesignPermission) {
    router.addRoute({
      path: '/bank-note/label-designer',
      name: 'BankNoteLabelDesigner',
      component: () => import('./index-new.vue'),
      meta: {
        title: '标签设计器',
        icon: 'DesktopOutlined',
        authority: 'banknote:label:design',
        keepAlive: false,
        hideFooter: true
      }
    })
  }

  // 注册旧版设计器路由（作为备用）
  if (enableLegacyDesigner && hasDesignPermission) {
    router.addRoute({
      path: '/bank-note/label-designer/legacy',
      name: 'BankNoteLabelDesignerLegacy',
      component: () => import('./index.vue'),
      meta: {
        title: '标签设计器(旧版)',
        icon: 'DesktopOutlined',
        authority: 'banknote:label:design',
        keepAlive: false,
        hideFooter: true,
        hidden: true
      }
    })
  }

  // 注册预览路由
  if (hasViewPermission) {
    router.addRoute({
      path: '/bank-note/label-designer/preview/:templateId',
      name: 'BankNoteLabelPreview',
      component: () => import('./components/TemplatePreview.vue'),
      meta: {
        title: '模板预览',
        authority: 'banknote:label:view',
        keepAlive: false,
        hideFooter: true,
        hidden: true
      }
    })
  }

  // 注册打印路由
  if (hasPrintPermission) {
    router.addRoute({
      path: '/bank-note/label-designer/print/:templateId',
      name: 'BankNoteLabelPrint',
      component: () => import('./components/PrintPage.vue'),
      meta: {
        title: '标签打印',
        authority: 'banknote:label:print',
        keepAlive: false,
        hideFooter: true,
        hidden: true
      }
    })
  }

  console.log('标签设计器路由注册完成')
}

/**
 * 路由守卫 - 检查设计器版本配置
 */
export function createLabelDesignerGuard(config = {}) {
  return (to, from, next) => {
    const {
      enableNewDesigner = true,
      enableLegacyDesigner = true,
      forceVersion = null
    } = config

    // 如果强制使用特定版本
    if (forceVersion) {
      if (forceVersion === 'new' && to.name === 'BankNoteLabelDesignerLegacy') {
        next({ name: 'BankNoteLabelDesigner' })
        return
      }
      if (forceVersion === 'legacy' && to.name === 'BankNoteLabelDesigner') {
        next({ name: 'BankNoteLabelDesignerLegacy' })
        return
      }
    }

    // 检查版本可用性
    if (to.name === 'BankNoteLabelDesigner' && !enableNewDesigner) {
      if (enableLegacyDesigner) {
        next({ name: 'BankNoteLabelDesignerLegacy' })
      } else {
        next(false)
      }
      return
    }

    if (to.name === 'BankNoteLabelDesignerLegacy' && !enableLegacyDesigner) {
      if (enableNewDesigner) {
        next({ name: 'BankNoteLabelDesigner' })
      } else {
        next(false)
      }
      return
    }

    next()
  }
}

/**
 * 获取设计器路由配置
 */
export function getLabelDesignerRouteConfig() {
  const config = {
    enableNewDesigner: true,
    enableLegacyDesigner: true,
    defaultDesigner: 'new',
    forceVersion: null
  }

  // 从环境变量读取配置
  if (import.meta.env.VITE_DESIGNER_VERSION) {
    config.defaultDesigner = import.meta.env.VITE_DESIGNER_VERSION
  }

  if (import.meta.env.VITE_FORCE_DESIGNER_VERSION) {
    config.forceVersion = import.meta.env.VITE_FORCE_DESIGNER_VERSION
  }

  // 从本地存储读取用户偏好
  const userPreference = localStorage.getItem('labelDesignerVersion')
  if (userPreference && ['new', 'legacy'].includes(userPreference)) {
    config.defaultDesigner = userPreference
  }

  return config
}

/**
 * 路由元信息扩展
 */
export const routeMetaExtensions = {
  // 标签设计器特有的元信息
  labelDesigner: {
    // 是否需要打印客户端
    requiresPrintClient: true,
    // 支持的浏览器
    supportedBrowsers: ['chrome', 'firefox', 'safari', 'edge'],
    // 最小屏幕分辨率
    minResolution: '1024x768',
    // 是否支持移动端
    mobileSupported: false
  }
}

/**
 * 路由跳转辅助函数
 */
export const labelDesignerNavigation = {
  /**
   * 跳转到设计器
   */
  toDesigner(router, version = 'auto') {
    const config = getLabelDesignerRouteConfig()
    
    let targetRoute = 'BankNoteLabelDesigner'
    
    if (version === 'legacy') {
      targetRoute = 'BankNoteLabelDesignerLegacy'
    } else if (version === 'auto') {
      targetRoute = config.defaultDesigner === 'legacy' 
        ? 'BankNoteLabelDesignerLegacy' 
        : 'BankNoteLabelDesigner'
    }
    
    router.push({ name: targetRoute })
  },

  /**
   * 跳转到模板预览
   */
  toPreview(router, templateId) {
    router.push({ 
      name: 'BankNoteLabelPreview', 
      params: { templateId } 
    })
  },

  /**
   * 跳转到打印页面
   */
  toPrint(router, templateId, options = {}) {
    router.push({ 
      name: 'BankNoteLabelPrint', 
      params: { templateId },
      query: options
    })
  },

  /**
   * 在新窗口打开预览
   */
  openPreviewInNewWindow(templateId) {
    const url = `/bank-note/label-designer/preview/${templateId}`
    window.open(url, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes')
  },

  /**
   * 在新窗口打开打印页面
   */
  openPrintInNewWindow(templateId, options = {}) {
    const queryString = new URLSearchParams(options).toString()
    const url = `/bank-note/label-designer/print/${templateId}${queryString ? '?' + queryString : ''}`
    window.open(url, '_blank', 'width=1000,height=700,scrollbars=yes,resizable=yes')
  }
}

/**
 * 路由权限检查
 */
export function checkLabelDesignerPermission(route, userPermissions = []) {
  const requiredPermission = route.meta?.authority
  
  if (!requiredPermission) {
    return true
  }
  
  return userPermissions.includes(requiredPermission)
}

/**
 * 浏览器兼容性检查
 */
export function checkBrowserCompatibility() {
  const userAgent = navigator.userAgent.toLowerCase()
  const supportedBrowsers = routeMetaExtensions.labelDesigner.supportedBrowsers
  
  const browserChecks = {
    chrome: /chrome/.test(userAgent) && !/edge/.test(userAgent),
    firefox: /firefox/.test(userAgent),
    safari: /safari/.test(userAgent) && !/chrome/.test(userAgent),
    edge: /edge/.test(userAgent)
  }
  
  const isSupported = supportedBrowsers.some(browser => browserChecks[browser])
  
  return {
    isSupported,
    currentBrowser: Object.keys(browserChecks).find(browser => browserChecks[browser]) || 'unknown',
    supportedBrowsers
  }
}

/**
 * 设备兼容性检查
 */
export function checkDeviceCompatibility() {
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  const screenWidth = window.screen.width
  const screenHeight = window.screen.height
  
  const minResolution = routeMetaExtensions.labelDesigner.minResolution.split('x')
  const minWidth = parseInt(minResolution[0])
  const minHeight = parseInt(minResolution[1])
  
  return {
    isMobile,
    mobileSupported: routeMetaExtensions.labelDesigner.mobileSupported,
    screenResolution: `${screenWidth}x${screenHeight}`,
    meetsMinResolution: screenWidth >= minWidth && screenHeight >= minHeight,
    minResolution: routeMetaExtensions.labelDesigner.minResolution
  }
}
