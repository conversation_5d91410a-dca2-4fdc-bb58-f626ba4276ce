# 银行券标签设计器部署指南

## 📋 部署概述

本指南详细说明如何在生产环境中部署新版银行券标签设计器，确保系统稳定运行并与现有环境兼容。

## 🔧 环境要求

### 前端环境
- **Node.js**: >= 16.x
- **Vue.js**: >= 3.x
- **Element Plus**: >= 2.x
- **现代浏览器**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+

### 后端环境
- **Java**: >= 8
- **Spring Boot**: >= 2.x
- **数据库**: MySQL 5.7+ / PostgreSQL 10+

### 网络要求
- **HTTPS**: 生产环境必须使用HTTPS（打印客户端连接要求）
- **WebSocket**: 支持WebSocket连接（直接打印功能）
- **CDN**: 建议使用CDN加速静态资源

## 📦 部署步骤

### 第一步：依赖安装

```bash
# 1. 安装核心依赖
npm install vue-plugin-hiprint

# 2. 验证依赖版本
npm list vue-plugin-hiprint
```

### 第二步：配置文件更新

#### 1. 更新 index.html
确保已添加必要的打印样式：

```html
<!-- 打印样式 - 必须添加 -->
<link
  rel="stylesheet"
  type="text/css"
  media="print"
  href="https://cdn.jsdelivr.net/npm/vue-plugin-hiprint@latest/dist/print-lock.css"
/>
```

#### 2. 环境配置
在 `.env.production` 中添加：

```env
# 打印服务配置
VITE_PRINT_SERVER_HOST=https://your-domain.com:17521
VITE_PRINT_SERVER_TOKEN=your-print-token

# 设计器配置
VITE_DESIGNER_VERSION=new
VITE_ENABLE_MIGRATION=true
VITE_ENABLE_LEGACY_FALLBACK=true
```

### 第三步：构建和部署

```bash
# 1. 构建生产版本
npm run build

# 2. 验证构建结果
ls -la dist/

# 3. 部署到服务器
# 方式1: 直接复制
cp -r dist/* /var/www/html/

# 方式2: 使用Docker
docker build -t label-designer:latest .
docker run -d -p 80:80 label-designer:latest
```

### 第四步：后端配置

#### 1. 数据库迁移
执行数据库迁移脚本：

```sql
-- 添加迁移标记字段
ALTER TABLE label_template ADD COLUMN migrated BOOLEAN DEFAULT FALSE;
ALTER TABLE label_template ADD COLUMN original_version VARCHAR(20) DEFAULT 'legacy';
ALTER TABLE label_template ADD COLUMN backup_time DATETIME NULL;

-- 创建迁移日志表
CREATE TABLE label_migration_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    template_id BIGINT,
    migration_type VARCHAR(50),
    status VARCHAR(20),
    error_message TEXT,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. 应用配置
在 `application.yml` 中添加：

```yaml
label-designer:
  # 设计器配置
  version: new
  enable-migration: true
  enable-legacy-fallback: true
  
  # 打印配置
  print:
    server-host: https://your-domain.com:17521
    server-token: ${PRINT_SERVER_TOKEN:}
    timeout: 30000
    
  # 模板配置
  template:
    max-size: 10MB
    backup-enabled: true
    backup-retention-days: 30
```

## 🔄 迁移策略

### 渐进式迁移
建议采用渐进式迁移策略，确保业务连续性：

#### 阶段1：并行运行（1-2周）
- 新旧版本同时可用
- 用户可自由选择版本
- 收集用户反馈

#### 阶段2：引导迁移（2-4周）
- 默认使用新版本
- 提供迁移向导
- 保留旧版本作为备用

#### 阶段3：完全迁移（1周）
- 停用旧版本
- 完成所有模板迁移
- 清理旧代码

### 数据迁移
```bash
# 1. 备份现有数据
mysqldump -u root -p database_name label_template > backup_templates.sql

# 2. 执行迁移脚本
node scripts/migrate-templates.js

# 3. 验证迁移结果
node scripts/verify-migration.js
```

## 🔍 验证和测试

### 功能验证清单

#### 基础功能
- [ ] 设计器正常加载
- [ ] 元素拖拽功能正常
- [ ] 模板保存和加载
- [ ] 样式配置功能
- [ ] 预览功能正常

#### 打印功能
- [ ] 打印客户端连接
- [ ] 单个标签打印
- [ ] 批量打印功能
- [ ] 打印质量验证
- [ ] 错误处理机制

#### 迁移功能
- [ ] 模板迁移向导
- [ ] 数据格式转换
- [ ] 迁移结果验证
- [ ] 回滚机制

### 性能测试
```bash
# 1. 页面加载性能
lighthouse https://your-domain.com/label-designer

# 2. 大量数据处理
# 测试1000+模板的迁移性能

# 3. 并发用户测试
# 模拟50+用户同时使用设计器
```

## 🚨 故障排除

### 常见问题及解决方案

#### 1. 打印样式丢失
**症状**: 打印时样式错乱或丢失
**解决**: 检查 print-lock.css 是否正确加载

```html
<!-- 确保样式文件路径正确 -->
<link rel="stylesheet" type="text/css" media="print" 
      href="/assets/print-lock.css" />
```

#### 2. hiprint 初始化失败
**症状**: 设计器无法加载，控制台报错
**解决**: 检查依赖版本和初始化顺序

```javascript
// 确保正确的初始化顺序
import { hiprint, defaultElementTypeProvider } from 'vue-plugin-hiprint'

// 在组件挂载后初始化
onMounted(async () => {
  await nextTick()
  hiprint.init({
    providers: [new defaultElementTypeProvider()]
  })
})
```

#### 3. 跨域问题
**症状**: 无法连接打印客户端
**解决**: 配置HTTPS和CORS

```nginx
# Nginx 配置
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    # SSL 配置
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    # CORS 配置
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
}
```

#### 4. 模板迁移失败
**症状**: 旧模板无法迁移到新格式
**解决**: 检查数据格式和迁移逻辑

```javascript
// 调试迁移过程
const migrationResult = migrateLegacyTemplate(template)
if (!migrationResult.success) {
  console.error('迁移失败:', migrationResult.errors)
  // 手动处理或联系技术支持
}
```

## 📊 监控和维护

### 性能监控
```javascript
// 添加性能监控
const observer = new PerformanceObserver((list) => {
  for (const entry of list.getEntries()) {
    if (entry.name.includes('hiprint')) {
      console.log('hiprint 性能:', entry.duration)
    }
  }
})
observer.observe({ entryTypes: ['measure'] })
```

### 错误监控
```javascript
// 全局错误处理
window.addEventListener('error', (event) => {
  if (event.error.stack.includes('hiprint')) {
    // 发送错误报告
    sendErrorReport({
      message: event.error.message,
      stack: event.error.stack,
      url: window.location.href
    })
  }
})
```

### 日志配置
```yaml
# logback-spring.xml
<logger name="com.payne.server.banknote.service.LabelDesignService" level="INFO"/>
<logger name="com.payne.server.banknote.service.BatchPrintService" level="INFO"/>

# 打印相关日志
<logger name="print" level="DEBUG"/>
```

## 🔒 安全考虑

### 数据安全
- 模板数据加密存储
- 用户权限验证
- 操作日志记录

### 网络安全
- HTTPS 强制使用
- CSP 策略配置
- XSS 防护

```html
<!-- CSP 配置 -->
<meta http-equiv="Content-Security-Policy" 
      content="default-src 'self'; 
               script-src 'self' 'unsafe-inline' cdn.jsdelivr.net;
               style-src 'self' 'unsafe-inline' cdn.jsdelivr.net;
               img-src 'self' data: blob:;">
```

## 📈 性能优化

### 前端优化
```javascript
// 1. 懒加载组件
const HiprintDesigner = defineAsyncComponent(() => 
  import('./components/HiprintDesigner.vue')
)

// 2. 缓存配置
const templateCache = new Map()

// 3. 防抖处理
const debouncedSave = debounce(saveTemplate, 1000)
```

### 后端优化
```java
// 1. 缓存配置
@Cacheable(value = "templates", key = "#id")
public LabelTemplate getTemplate(Long id) {
    return templateRepository.findById(id);
}

// 2. 异步处理
@Async
public CompletableFuture<Void> batchMigrate(List<LabelTemplate> templates) {
    // 异步迁移逻辑
}
```

## 🔄 回滚计划

### 回滚触发条件
- 严重功能故障
- 性能严重下降
- 用户投诉激增

### 回滚步骤
```bash
# 1. 停止新版本服务
systemctl stop label-designer-new

# 2. 恢复旧版本
systemctl start label-designer-legacy

# 3. 恢复数据库
mysql -u root -p database_name < backup_templates.sql

# 4. 清理缓存
redis-cli FLUSHDB
```

## 📞 技术支持

### 联系方式
- **技术支持邮箱**: <EMAIL>
- **紧急联系电话**: +86-xxx-xxxx-xxxx
- **在线文档**: https://docs.company.com/label-designer

### 支持时间
- **工作日**: 9:00-18:00
- **紧急支持**: 24/7（严重故障）

---

**重要提醒**: 
1. 生产环境部署前务必在测试环境完整验证
2. 建议在业务低峰期进行部署
3. 准备好回滚方案以应对突发情况
4. 部署后持续监控系统状态
