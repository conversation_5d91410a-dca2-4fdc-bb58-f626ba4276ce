/**
 * 银行券标签设计器集成测试
 * @since 2025-01-27
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElMessage } from 'element-plus'
import HiprintDesigner from '../components/HiprintDesigner.vue'
import BatchPrintIntegration from '../components/BatchPrintIntegration.vue'
import MigrationWizard from '../components/MigrationWizard.vue'
import {
  convertLabelZonesToHiprint,
  convertHiprintToLabelZones,
  createDefaultLargeTemplate,
  createDefaultSmallTemplate,
  validateTemplateData
} from '../utils/dataAdapter'
import {
  migrateLegacyTemplate,
  batchMigrateLegacyTemplates,
  generateMigrationReport
} from '../utils/migrationTool'

// Mock vue-plugin-hiprint
vi.mock('vue-plugin-hiprint', () => ({
  hiprint: {
    init: vi.fn(),
    disAutoConnect: vi.fn(),
    PrintTemplate: vi.fn(() => ({
      design: vi.fn(),
      update: vi.fn(),
      getJson: vi.fn(() => ({})),
      print: vi.fn(),
      print2: vi.fn(),
      preview: vi.fn(),
      undo: vi.fn(),
      redo: vi.fn(),
      canUndo: vi.fn(() => false),
      canRedo: vi.fn(() => false),
      destroy: vi.fn()
    })),
    PrintElementTypeManager: {
      buildByHtml: vi.fn()
    }
  },
  defaultElementTypeProvider: vi.fn()
}))

// Mock API
vi.mock('../api', () => ({
  getFieldsByCategory: vi.fn(() => Promise.resolve({
    BASIC_INFO: [
      { fieldName: 'bankName', displayName: '银行名称', category: 'BASIC_INFO' },
      { fieldName: 'coinName1', displayName: '钱币名称1', category: 'BASIC_INFO' }
    ],
    GRADE_INFO: [
      { fieldName: 'gradeScore', displayName: '评级分数', category: 'GRADE_INFO' },
      { fieldName: 'gradeLevel', displayName: '评级等级', category: 'GRADE_INFO' }
    ]
  })),
  saveTemplate: vi.fn(() => Promise.resolve({ id: 1 })),
  getTemplateList: vi.fn(() => Promise.resolve([])),
  deleteTemplate: vi.fn(() => Promise.resolve()),
  previewLabel: vi.fn(() => Promise.resolve())
}))

describe('数据适配器测试', () => {
  describe('convertLabelZonesToHiprint', () => {
    it('应该正确转换旧版区域数据到hiprint格式', () => {
      const labelZones = [
        {
          id: 'coinInfo',
          name: '钱币信息',
          x: 30,
          y: 0,
          width: 100,
          height: 26,
          fontSize: 10,
          color: '#333333',
          fields: ['bankName', 'coinName1']
        }
      ]

      const result = convertLabelZonesToHiprint(labelZones, { width: 192, height: 26 })

      expect(result).toHaveProperty('panels')
      expect(result.panels).toHaveLength(1)
      expect(result.panels[0]).toHaveProperty('printElements')
      expect(result.panels[0].printElements).toHaveLength(1)
      
      const element = result.panels[0].printElements[0]
      expect(element.type).toBe('text')
      expect(element.options.field).toBe('bankName')
    })

    it('应该处理Logo区域', () => {
      const labelZones = [
        {
          id: 'logo',
          name: '公司Logo位置(预留)',
          x: 0,
          y: 0,
          width: 30,
          height: 26,
          fields: []
        }
      ]

      const result = convertLabelZonesToHiprint(labelZones)
      const element = result.panels[0].printElements[0]
      
      expect(element.type).toBe('image')
      expect(element.options.src).toBe('')
    })
  })

  describe('convertHiprintToLabelZones', () => {
    it('应该正确转换hiprint格式到旧版区域数据', () => {
      const hiprintTemplate = {
        panels: [
          {
            printElements: [
              {
                type: 'text',
                options: {
                  left: 85,
                  top: 0,
                  width: 283,
                  height: 74,
                  fontSize: 10,
                  color: '#333333',
                  field: 'bankName',
                  title: '银行名称'
                }
              }
            ]
          }
        ]
      }

      const result = convertHiprintToLabelZones(hiprintTemplate)

      expect(result).toHaveLength(1)
      expect(result[0]).toHaveProperty('id')
      expect(result[0]).toHaveProperty('fields')
      expect(result[0].fields).toContain('bankName')
    })
  })

  describe('createDefaultTemplates', () => {
    it('应该创建有效的大签默认模板', () => {
      const template = createDefaultLargeTemplate()
      const validation = validateTemplateData(template)
      
      expect(validation.valid).toBe(true)
      expect(template.panels[0].width).toBe(192)
      expect(template.panels[0].height).toBe(26)
    })

    it('应该创建有效的小签默认模板', () => {
      const template = createDefaultSmallTemplate()
      const validation = validateTemplateData(template)
      
      expect(validation.valid).toBe(true)
      expect(template.panels[0].width).toBe(115)
      expect(template.panels[0].height).toBe(21)
    })
  })

  describe('validateTemplateData', () => {
    it('应该验证有效的模板数据', () => {
      const validTemplate = createDefaultLargeTemplate()
      const result = validateTemplateData(validTemplate)
      
      expect(result.valid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('应该检测无效的模板数据', () => {
      const invalidTemplate = null
      const result = validateTemplateData(invalidTemplate)
      
      expect(result.valid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
    })
  })
})

describe('迁移工具测试', () => {
  describe('migrateLegacyTemplate', () => {
    it('应该成功迁移有效的旧版模板', () => {
      const legacyTemplate = {
        id: 1,
        templateName: '测试模板',
        templateType: 'CUSTOM',
        layoutConfig: JSON.stringify({
          zones: [
            {
              id: 'coinInfo',
              name: '钱币信息',
              x: 30,
              y: 0,
              width: 100,
              height: 26,
              fields: ['bankName']
            }
          ],
          canvas: { width: 192, height: 26 }
        }),
        fieldMapping: JSON.stringify({
          coinInfo: ['bankName']
        })
      }

      const result = migrateLegacyTemplate(legacyTemplate)

      expect(result.success).toBe(true)
      expect(result.data).toHaveProperty('layoutConfig')
      expect(result.data.migrated).toBe(true)
      expect(result.stats.originalZones).toBe(1)
    })

    it('应该处理迁移错误', () => {
      const invalidTemplate = {
        layoutConfig: 'invalid json'
      }

      const result = migrateLegacyTemplate(invalidTemplate)

      expect(result.success).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
    })
  })

  describe('batchMigrateLegacyTemplates', () => {
    it('应该批量迁移多个模板', () => {
      const templates = [
        {
          templateName: '模板1',
          layoutConfig: JSON.stringify({
            zones: [{ id: 'test', x: 0, y: 0, width: 50, height: 20 }],
            canvas: { width: 192, height: 26 }
          })
        },
        {
          templateName: '模板2',
          layoutConfig: 'invalid'
        }
      ]

      const result = batchMigrateLegacyTemplates(templates)

      expect(result.total).toBe(2)
      expect(result.success).toBe(1)
      expect(result.failed).toBe(1)
    })
  })

  describe('generateMigrationReport', () => {
    it('应该生成迁移报告', () => {
      const migrationResult = {
        total: 2,
        success: 1,
        failed: 1,
        summary: {
          errors: [{ templateName: '模板2', errors: ['格式错误'] }],
          warnings: []
        }
      }

      const report = generateMigrationReport(migrationResult)

      expect(report).toContain('# 模板迁移报告')
      expect(report).toContain('总数: 2')
      expect(report).toContain('成功: 1')
      expect(report).toContain('失败: 1')
    })
  })
})

describe('组件集成测试', () => {
  let wrapper

  beforeEach(() => {
    // Mock DOM elements
    global.document.querySelector = vi.fn(() => ({
      __vue__: {
        toggleAllSelection: vi.fn(),
        clearSelection: vi.fn()
      }
    }))
    global.document.getElementById = vi.fn(() => ({}))
  })

  describe('HiprintDesigner', () => {
    it('应该正确初始化设计器', async () => {
      wrapper = mount(HiprintDesigner, {
        global: {
          mocks: {
            $: vi.fn(() => ({
              length: 1
            }))
          }
        }
      })

      expect(wrapper.exists()).toBe(true)
      expect(wrapper.find('.hiprint-designer').exists()).toBe(true)
    })

    it('应该处理模板保存', async () => {
      wrapper = mount(HiprintDesigner)
      
      // 模拟保存操作
      await wrapper.vm.saveTemplate()
      
      // 验证保存逻辑
      expect(wrapper.vm.saving).toBe(false)
    })
  })

  describe('BatchPrintIntegration', () => {
    it('应该正确渲染批量打印组件', () => {
      wrapper = mount(BatchPrintIntegration, {
        props: {
          hiprintTemplate: {},
          currentTemplate: { templateName: '测试模板' }
        }
      })

      expect(wrapper.exists()).toBe(true)
      expect(wrapper.find('.batch-print-integration').exists()).toBe(true)
    })
  })

  describe('MigrationWizard', () => {
    it('应该正确渲染迁移向导', () => {
      wrapper = mount(MigrationWizard, {
        props: {
          visible: true
        }
      })

      expect(wrapper.exists()).toBe(true)
      expect(wrapper.find('.migration-wizard').exists()).toBe(true)
    })
  })
})

describe('错误处理测试', () => {
  it('应该处理API错误', async () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    
    // 模拟API错误
    vi.mocked(require('../api').getFieldsByCategory).mockRejectedValueOnce(new Error('API错误'))
    
    wrapper = mount(HiprintDesigner)
    await wrapper.vm.$nextTick()
    
    expect(consoleSpy).toHaveBeenCalled()
    consoleSpy.mockRestore()
  })

  it('应该处理hiprint初始化错误', async () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    
    // 模拟hiprint错误
    vi.mocked(require('vue-plugin-hiprint').hiprint.init).mockImplementationOnce(() => {
      throw new Error('hiprint初始化失败')
    })
    
    wrapper = mount(HiprintDesigner)
    await wrapper.vm.$nextTick()
    
    expect(consoleSpy).toHaveBeenCalled()
    consoleSpy.mockRestore()
  })
})

describe('性能测试', () => {
  it('应该在合理时间内完成大量数据的迁移', () => {
    const startTime = Date.now()
    
    // 创建大量测试数据
    const templates = Array.from({ length: 100 }, (_, i) => ({
      templateName: `模板${i}`,
      layoutConfig: JSON.stringify({
        zones: [{ id: `zone${i}`, x: 0, y: 0, width: 50, height: 20 }],
        canvas: { width: 192, height: 26 }
      })
    }))
    
    const result = batchMigrateLegacyTemplates(templates)
    
    const endTime = Date.now()
    const duration = endTime - startTime
    
    expect(duration).toBeLessThan(5000) // 应该在5秒内完成
    expect(result.total).toBe(100)
  })
})

describe('兼容性测试', () => {
  it('应该与现有API格式兼容', () => {
    const legacyData = {
      templateName: '兼容性测试',
      templateType: 'CUSTOM',
      layoutConfig: JSON.stringify({
        zones: [
          {
            id: 'coinInfo',
            name: '钱币信息',
            x: 30,
            y: 0,
            width: 100,
            height: 26,
            fontSize: 10,
            color: '#333333',
            backgroundColor: '#ffffff',
            fields: ['bankName', 'coinName1', 'serialNumber']
          }
        ],
        canvas: { width: 192, height: 26 }
      }),
      fieldMapping: JSON.stringify({
        coinInfo: ['bankName', 'coinName1', 'serialNumber']
      })
    }

    const migrationResult = migrateLegacyTemplate(legacyData)
    
    expect(migrationResult.success).toBe(true)
    expect(migrationResult.data).toHaveProperty('templateName')
    expect(migrationResult.data).toHaveProperty('templateType')
    expect(migrationResult.data).toHaveProperty('layoutConfig')
    expect(migrationResult.data).toHaveProperty('fieldMapping')
  })
})
