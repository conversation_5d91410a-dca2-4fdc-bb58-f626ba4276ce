<template>
  <div class="batch-print-dialog">
    <el-steps :active="currentStep" finish-status="success">
      <el-step title="选择数据" description="选择要打印的钱币数据" />
      <el-step title="配置打印" description="配置打印参数" />
      <el-step title="预览确认" description="预览打印效果" />
      <el-step title="执行打印" description="执行批量打印" />
    </el-steps>

    <div class="step-content">
      <!-- 步骤1: 选择数据 -->
      <div v-if="currentStep === 0" class="step-select-data">
        <div class="data-filter">
          <el-form :model="filterForm" inline>
            <el-form-item label="钱币类型">
              <el-select v-model="filterForm.coinType" placeholder="全部">
                <el-option label="全部" value="" />
                <el-option label="纸币" value="纸币" />
                <el-option label="硬币" value="硬币" />
              </el-select>
            </el-form-item>
            <el-form-item label="审核状态">
              <el-select v-model="filterForm.auditStatus" placeholder="全部">
                <el-option label="全部" value="" />
                <el-option label="已审核" value="1" />
                <el-option label="待审核" value="0" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="loadCoinData">查询</el-button>
            </el-form-item>
          </el-form>
        </div>

        <el-table
          ref="dataTable"
          :data="coinData"
          @selection-change="handleSelectionChange"
          height="400"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="diyCode" label="送评条码" width="120" />
          <el-table-column prop="bankName" label="银行名称" width="120" />
          <el-table-column prop="coinName1" label="钱币名称" width="150" />
          <el-table-column prop="serialNumber" label="编号" width="120" />
          <el-table-column prop="gradeScore" label="评级分数" width="100" />
          <el-table-column prop="gradeLevel" label="评级等级" width="100" />
          <el-table-column prop="auditStatus" label="审核状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.auditStatus === 1 ? 'success' : 'warning'">
                {{ row.auditStatus === 1 ? '已审核' : '待审核' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <div class="selection-info">
          <span>已选择 {{ selectedCoins.length }} 条数据</span>
          <el-button type="text" @click="selectAll">全选</el-button>
          <el-button type="text" @click="clearSelection">清空</el-button>
        </div>
      </div>

      <!-- 步骤2: 配置打印 -->
      <div v-if="currentStep === 1" class="step-print-config">
        <el-form :model="printConfig" label-width="120px">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="打印机" required>
                <el-select v-model="printConfig.printer" placeholder="选择打印机">
                  <el-option
                    v-for="printer in printerList"
                    :key="printer.name"
                    :label="printer.name"
                    :value="printer.name"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="打印份数">
                <el-input-number
                  v-model="printConfig.copies"
                  :min="1"
                  :max="10"
                  controls-position="right"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="打印质量">
                <el-select v-model="printConfig.quality">
                  <el-option label="草稿" value="draft" />
                  <el-option label="标准" value="normal" />
                  <el-option label="高质量" value="high" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="打印方向">
                <el-select v-model="printConfig.orientation">
                  <el-option label="横向" value="landscape" />
                  <el-option label="纵向" value="portrait" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="打印标题">
            <el-input v-model="printConfig.title" placeholder="批量标签打印" />
          </el-form-item>
        </el-form>

        <div class="field-mapping-config">
          <h4>字段映射配置</h4>
          <el-table :data="fieldMappings" border>
            <el-table-column prop="templateField" label="模板字段" width="150" />
            <el-table-column prop="displayName" label="显示名称" width="150" />
            <el-table-column label="数据字段" width="200">
              <template #default="{ row }">
                <el-select v-model="row.dataField" placeholder="选择数据字段">
                  <el-option
                    v-for="field in availableFields"
                    :key="field.value"
                    :label="field.label"
                    :value="field.value"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="示例数据">
              <template #default="{ row }">
                <el-tag size="small">{{ getFieldSampleData(row.dataField) }}</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 步骤3: 预览确认 -->
      <div v-if="currentStep === 2" class="step-preview">
        <div class="preview-info">
          <el-alert
            title="预览信息"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>将要打印 {{ selectedCoins.length }} 个标签</p>
              <p>打印机: {{ printConfig.printer }}</p>
              <p>打印份数: {{ printConfig.copies }}</p>
            </template>
          </el-alert>
        </div>

        <div class="preview-container">
          <h4>打印预览 (显示前3个)</h4>
          <div class="preview-items">
            <div
              v-for="(coin, index) in previewCoins"
              :key="coin.id"
              class="preview-item"
            >
              <div class="preview-header">
                <span>第 {{ index + 1 }} 个标签</span>
                <el-tag size="small">{{ coin.diyCode }}</el-tag>
              </div>
              <div :id="`preview-${index}`" class="preview-content"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤4: 执行打印 -->
      <div v-if="currentStep === 3" class="step-execute">
        <div class="print-progress">
          <el-progress
            :percentage="printProgress"
            :status="printStatus"
            :stroke-width="20"
          />
          <p class="progress-text">{{ printProgressText }}</p>
        </div>

        <div class="print-log">
          <h4>打印日志</h4>
          <div class="log-container">
            <div
              v-for="(log, index) in printLogs"
              :key="index"
              class="log-item"
              :class="log.type"
            >
              <span class="log-time">{{ formatTime(log.time) }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="dialog-footer">
      <el-button @click="$emit('close')">取消</el-button>
      <el-button v-if="currentStep > 0" @click="prevStep">上一步</el-button>
      <el-button
        v-if="currentStep < 3"
        type="primary"
        @click="nextStep"
        :disabled="!canNextStep"
      >
        下一步
      </el-button>
      <el-button
        v-if="currentStep === 3"
        type="primary"
        @click="startPrint"
        :loading="printing"
        :disabled="printCompleted"
      >
        开始打印
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { hiprint } from 'vue-plugin-hiprint'
import { queryPrintCoins, generatePrintData } from '../../batch-print/api'

// Props
const props = defineProps({
  templateData: {
    type: Object,
    required: true
  },
  fieldMappings: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['print-completed', 'close'])

// 响应式数据
const currentStep = ref(0)
const coinData = ref([])
const selectedCoins = ref([])
const printerList = ref([])
const printing = ref(false)
const printCompleted = ref(false)
const printProgress = ref(0)
const printStatus = ref('')
const printProgressText = ref('')
const printLogs = ref([])

const filterForm = reactive({
  coinType: '',
  auditStatus: ''
})

const printConfig = reactive({
  printer: '',
  copies: 1,
  quality: 'normal',
  orientation: 'landscape',
  title: '批量标签打印'
})

const fieldMappings = ref([])

// 计算属性
const canNextStep = computed(() => {
  switch (currentStep.value) {
    case 0:
      return selectedCoins.value.length > 0
    case 1:
      return printConfig.printer && fieldMappings.value.every(m => m.dataField)
    case 2:
      return true
    default:
      return false
  }
})

const previewCoins = computed(() => {
  return selectedCoins.value.slice(0, 3)
})

const availableFields = computed(() => {
  return [
    { label: '银行名称', value: 'bankName' },
    { label: '钱币名称1', value: 'coinName1' },
    { label: '钱币名称2', value: 'coinName2' },
    { label: '编号', value: 'serialNumber' },
    { label: '版别', value: 'version' },
    { label: '评级分数', value: 'gradeScore' },
    { label: '评级等级', value: 'gradeLevel' },
    { label: '特殊标记', value: 'specialMark' },
    { label: '送评条码', value: 'diyCode' },
    { label: '年代信息', value: 'yearInfo' },
    { label: '面值', value: 'faceValue' },
    { label: '钱币类型', value: 'coinType' }
  ]
})

// 方法
const loadCoinData = async () => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 1000,
      ...filterForm
    }
    const result = await queryPrintCoins(params)
    coinData.value = result.records || []
  } catch (error) {
    console.error('加载钱币数据失败:', error)
    ElMessage.error('加载钱币数据失败')
  }
}

const handleSelectionChange = (selection) => {
  selectedCoins.value = selection
}

const selectAll = () => {
  const table = document.querySelector('.el-table')
  if (table) {
    table.__vue__?.toggleAllSelection()
  }
}

const clearSelection = () => {
  const table = document.querySelector('.el-table')
  if (table) {
    table.__vue__?.clearSelection()
  }
}

const getFieldSampleData = (fieldName) => {
  if (!fieldName || selectedCoins.value.length === 0) return '无数据'
  return selectedCoins.value[0][fieldName] || '无数据'
}

const nextStep = async () => {
  if (currentStep.value === 2) {
    await generatePreview()
  }
  currentStep.value++
}

const prevStep = () => {
  currentStep.value--
}

const generatePreview = async () => {
  try {
    await nextTick()
    
    // 为前3个钱币生成预览
    previewCoins.value.forEach((coin, index) => {
      const previewContainer = document.getElementById(`preview-${index}`)
      if (previewContainer && props.templateData) {
        // 创建临时模板实例用于预览
        const tempTemplate = new hiprint.PrintTemplate({
          template: props.templateData
        })
        
        // 映射数据
        const mappedData = {}
        fieldMappings.value.forEach(mapping => {
          mappedData[mapping.templateField] = coin[mapping.dataField] || ''
        })
        
        tempTemplate.preview(previewContainer, mappedData)
      }
    })
  } catch (error) {
    console.error('生成预览失败:', error)
    ElMessage.error('生成预览失败')
  }
}

const startPrint = async () => {
  if (!printConfig.printer) {
    ElMessage.error('请选择打印机')
    return
  }

  printing.value = true
  printProgress.value = 0
  printStatus.value = ''
  printLogs.value = []

  try {
    addLog('开始批量打印...', 'info')
    
    const total = selectedCoins.value.length
    let completed = 0

    for (const coin of selectedCoins.value) {
      try {
        // 映射数据
        const mappedData = {}
        fieldMappings.value.forEach(mapping => {
          mappedData[mapping.templateField] = coin[mapping.dataField] || ''
        })

        // 创建打印实例
        const printTemplate = new hiprint.PrintTemplate({
          template: props.templateData
        })

        // 执行打印
        await printTemplate.print2(mappedData, {
          printer: printConfig.printer,
          title: `${printConfig.title} - ${coin.diyCode}`,
          copies: printConfig.copies
        })

        completed++
        printProgress.value = Math.round((completed / total) * 100)
        printProgressText.value = `正在打印 ${completed}/${total}`
        
        addLog(`成功打印: ${coin.diyCode}`, 'success')
        
        // 添加延迟避免打印队列过载
        await new Promise(resolve => setTimeout(resolve, 100))
        
      } catch (error) {
        addLog(`打印失败: ${coin.diyCode} - ${error.message}`, 'error')
      }
    }

    printStatus.value = 'success'
    printProgressText.value = `打印完成 ${completed}/${total}`
    printCompleted.value = true
    
    addLog('批量打印完成', 'success')
    
    emit('print-completed', {
      total,
      completed,
      failed: total - completed
    })

  } catch (error) {
    console.error('批量打印失败:', error)
    printStatus.value = 'exception'
    addLog(`批量打印失败: ${error.message}`, 'error')
  } finally {
    printing.value = false
  }
}

const addLog = (message, type = 'info') => {
  printLogs.value.push({
    time: new Date(),
    message,
    type
  })
}

const formatTime = (time) => {
  return time.toLocaleTimeString('zh-CN')
}

// 生命周期
onMounted(() => {
  // 初始化字段映射
  fieldMappings.value = props.fieldMappings.map(mapping => ({ ...mapping }))
  
  // 加载数据
  loadCoinData()
})
</script>

<style scoped>
.batch-print-dialog {
  padding: 20px 0;
}

.step-content {
  margin: 24px 0;
  min-height: 400px;
}

.data-filter {
  margin-bottom: 16px;
}

.selection-info {
  margin-top: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.field-mapping-config {
  margin-top: 24px;
}

.field-mapping-config h4 {
  margin: 0 0 16px 0;
}

.preview-info {
  margin-bottom: 24px;
}

.preview-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.preview-item {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 16px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
}

.preview-content {
  min-height: 100px;
  border: 1px dashed #ddd;
  border-radius: 4px;
  background: #fafafa;
}

.print-progress {
  text-align: center;
  margin-bottom: 24px;
}

.progress-text {
  margin-top: 8px;
  color: #666;
}

.print-log h4 {
  margin: 0 0 16px 0;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 8px;
  background: #fafafa;
}

.log-item {
  display: flex;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 13px;
}

.log-time {
  color: #999;
  min-width: 80px;
}

.log-item.success .log-message {
  color: #67c23a;
}

.log-item.error .log-message {
  color: #f56c6c;
}

.log-item.info .log-message {
  color: #409eff;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e8e8e8;
}
</style>
