<template>
  <div class="migration-wizard">
    <el-dialog
      v-model="visible"
      title="模板迁移向导"
      width="80%"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <el-steps :active="currentStep" finish-status="success">
        <el-step title="选择模板" description="选择要迁移的模板" />
        <el-step title="迁移预览" description="预览迁移结果" />
        <el-step title="执行迁移" description="执行迁移操作" />
        <el-step title="完成" description="迁移完成" />
      </el-steps>

      <div class="wizard-content">
        <!-- 步骤1: 选择模板 -->
        <div v-if="currentStep === 0" class="step-select">
          <el-alert
            title="模板迁移说明"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>此向导将帮助您将旧版标签设计器的模板迁移到新版设计器。</p>
              <ul>
                <li>✅ 自动转换区域布局为 hiprint 元素</li>
                <li>✅ 保留字段映射关系</li>
                <li>✅ 转换样式配置</li>
                <li>⚠️ 复杂布局可能需要手动调整</li>
              </ul>
            </template>
          </el-alert>

          <div class="template-selection">
            <h4>选择要迁移的模板</h4>
            <el-table
              ref="templateTable"
              :data="legacyTemplates"
              @selection-change="handleTemplateSelection"
              height="300"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="templateName" label="模板名称" />
              <el-table-column prop="templateType" label="模板类型" />
              <el-table-column prop="createTime" label="创建时间" width="180">
                <template #default="{ row }">
                  {{ formatDate(row.createTime) }}
                </template>
              </el-table-column>
              <el-table-column label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.migrated ? 'success' : 'info'">
                    {{ row.migrated ? '已迁移' : '未迁移' }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>

            <div class="selection-info">
              <span>已选择 {{ selectedTemplates.length }} 个模板</span>
              <el-button type="text" @click="selectAllTemplates">全选</el-button>
              <el-button type="text" @click="clearTemplateSelection">清空</el-button>
            </div>
          </div>
        </div>

        <!-- 步骤2: 迁移预览 -->
        <div v-if="currentStep === 1" class="step-preview">
          <div class="preview-header">
            <h4>迁移预览</h4>
            <el-button @click="previewMigration" :loading="previewing">
              重新预览
            </el-button>
          </div>

          <div v-if="migrationPreview" class="preview-content">
            <el-alert
              :title="`预览结果: ${migrationPreview.success}/${migrationPreview.total} 个模板可以成功迁移`"
              :type="migrationPreview.success === migrationPreview.total ? 'success' : 'warning'"
              :closable="false"
              show-icon
            />

            <el-tabs v-model="activePreviewTab" type="card">
              <el-tab-pane label="成功" name="success">
                <div v-if="successfulPreviews.length > 0">
                  <el-table :data="successfulPreviews" height="300">
                    <el-table-column prop="templateName" label="模板名称" />
                    <el-table-column label="转换统计" width="200">
                      <template #default="{ row }">
                        <div class="stats">
                          <span>区域: {{ row.stats?.originalZones || 0 }}</span>
                          <span>元素: {{ row.stats?.convertedElements || 0 }}</span>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column label="警告" width="100">
                      <template #default="{ row }">
                        <el-tag v-if="row.warnings?.length > 0" type="warning" size="small">
                          {{ row.warnings.length }}
                        </el-tag>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <el-empty v-else description="没有可成功迁移的模板" />
              </el-tab-pane>

              <el-tab-pane label="失败" name="failed">
                <div v-if="failedPreviews.length > 0">
                  <el-table :data="failedPreviews" height="300">
                    <el-table-column prop="templateName" label="模板名称" />
                    <el-table-column label="错误信息">
                      <template #default="{ row }">
                        <div class="errors">
                          <div v-for="error in row.errors" :key="error" class="error-item">
                            {{ error }}
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <el-empty v-else description="所有模板都可以成功迁移" />
              </el-tab-pane>

              <el-tab-pane label="警告" name="warnings">
                <div v-if="warningPreviews.length > 0">
                  <el-table :data="warningPreviews" height="300">
                    <el-table-column prop="templateName" label="模板名称" />
                    <el-table-column label="警告信息">
                      <template #default="{ row }">
                        <div class="warnings">
                          <div v-for="warning in row.warnings" :key="warning" class="warning-item">
                            {{ warning }}
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <el-empty v-else description="没有警告信息" />
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>

        <!-- 步骤3: 执行迁移 -->
        <div v-if="currentStep === 2" class="step-execute">
          <div class="migration-options">
            <h4>迁移选项</h4>
            <el-form :model="migrationOptions" label-width="120px">
              <el-form-item label="创建备份">
                <el-switch v-model="migrationOptions.createBackup" />
                <span class="option-desc">为原模板创建备份</span>
              </el-form-item>
              <el-form-item label="覆盖已迁移">
                <el-switch v-model="migrationOptions.overwriteExisting" />
                <span class="option-desc">覆盖已经迁移过的模板</span>
              </el-form-item>
              <el-form-item label="保留原模板">
                <el-switch v-model="migrationOptions.keepOriginal" />
                <span class="option-desc">保留原始模板不删除</span>
              </el-form-item>
            </el-form>
          </div>

          <div class="migration-progress" v-if="migrating">
            <el-progress
              :percentage="migrationProgress"
              :status="migrationStatus"
              :stroke-width="20"
            />
            <p class="progress-text">{{ migrationProgressText }}</p>
          </div>

          <div class="migration-log" v-if="migrationLogs.length > 0">
            <h4>迁移日志</h4>
            <div class="log-container">
              <div
                v-for="(log, index) in migrationLogs"
                :key="index"
                class="log-item"
                :class="log.type"
              >
                <span class="log-time">{{ formatTime(log.time) }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤4: 完成 -->
        <div v-if="currentStep === 3" class="step-complete">
          <div class="completion-summary">
            <el-result
              :icon="migrationResult?.success === migrationResult?.total ? 'success' : 'warning'"
              :title="migrationResult?.success === migrationResult?.total ? '迁移完成' : '迁移部分完成'"
            >
              <template #sub-title>
                <div class="result-stats">
                  <p>总计: {{ migrationResult?.total || 0 }} 个模板</p>
                  <p>成功: {{ migrationResult?.success || 0 }} 个</p>
                  <p>失败: {{ migrationResult?.failed || 0 }} 个</p>
                </div>
              </template>
              <template #extra>
                <el-button type="primary" @click="downloadReport">下载迁移报告</el-button>
                <el-button @click="viewMigratedTemplates">查看迁移的模板</el-button>
              </template>
            </el-result>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="wizard-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button v-if="currentStep > 0" @click="prevStep">上一步</el-button>
          <el-button
            v-if="currentStep < 3"
            type="primary"
            @click="nextStep"
            :disabled="!canNextStep"
          >
            下一步
          </el-button>
          <el-button
            v-if="currentStep === 2"
            type="primary"
            @click="startMigration"
            :loading="migrating"
            :disabled="migrationCompleted"
          >
            开始迁移
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  migrateLegacyTemplate,
  batchMigrateLegacyTemplates,
  generateMigrationReport,
  createMigrationBackup
} from '../utils/migrationTool'
import { getTemplateList, saveTemplate } from '../api'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'migration-completed'])

// 响应式数据
const currentStep = ref(0)
const legacyTemplates = ref([])
const selectedTemplates = ref([])
const migrationPreview = ref(null)
const previewing = ref(false)
const migrating = ref(false)
const migrationCompleted = ref(false)
const migrationProgress = ref(0)
const migrationStatus = ref('')
const migrationProgressText = ref('')
const migrationLogs = ref([])
const migrationResult = ref(null)
const activePreviewTab = ref('success')

const migrationOptions = reactive({
  createBackup: true,
  overwriteExisting: false,
  keepOriginal: true
})

// 计算属性
const canNextStep = computed(() => {
  switch (currentStep.value) {
    case 0:
      return selectedTemplates.value.length > 0
    case 1:
      return migrationPreview.value && migrationPreview.value.success > 0
    case 2:
      return true
    default:
      return false
  }
})

const successfulPreviews = computed(() => {
  return migrationPreview.value?.results?.filter(r => r.success) || []
})

const failedPreviews = computed(() => {
  return migrationPreview.value?.results?.filter(r => !r.success) || []
})

const warningPreviews = computed(() => {
  return migrationPreview.value?.results?.filter(r => r.warnings?.length > 0) || []
})

// 方法
const loadLegacyTemplates = async () => {
  try {
    const templates = await getTemplateList()
    // 过滤出旧版模板（没有 migrated 标记的）
    legacyTemplates.value = templates.filter(t => !t.migrated)
  } catch (error) {
    console.error('加载模板失败:', error)
    ElMessage.error('加载模板失败')
  }
}

const handleTemplateSelection = (selection) => {
  selectedTemplates.value = selection
}

const selectAllTemplates = () => {
  const table = document.querySelector('.el-table')
  if (table) {
    table.__vue__?.toggleAllSelection()
  }
}

const clearTemplateSelection = () => {
  const table = document.querySelector('.el-table')
  if (table) {
    table.__vue__?.clearSelection()
  }
}

const previewMigration = async () => {
  if (selectedTemplates.value.length === 0) {
    ElMessage.warning('请先选择要迁移的模板')
    return
  }

  previewing.value = true
  try {
    migrationPreview.value = batchMigrateLegacyTemplates(selectedTemplates.value)
    console.log('迁移预览结果:', migrationPreview.value)
  } catch (error) {
    console.error('预览迁移失败:', error)
    ElMessage.error('预览迁移失败')
  } finally {
    previewing.value = false
  }
}

const nextStep = async () => {
  if (currentStep.value === 0) {
    await previewMigration()
  }
  currentStep.value++
}

const prevStep = () => {
  currentStep.value--
}

const startMigration = async () => {
  if (!migrationPreview.value) {
    ElMessage.error('请先预览迁移结果')
    return
  }

  migrating.value = true
  migrationProgress.value = 0
  migrationLogs.value = []

  try {
    addLog('开始迁移模板...', 'info')

    const successfulTemplates = migrationPreview.value.results.filter(r => r.success)
    const total = successfulTemplates.length
    let completed = 0

    for (const result of successfulTemplates) {
      try {
        // 创建备份
        if (migrationOptions.createBackup) {
          const backup = createMigrationBackup(selectedTemplates.value[result.index])
          // 这里可以保存备份到数据库
          addLog(`已创建备份: ${result.templateName}`, 'info')
        }

        // 保存迁移后的模板
        await saveTemplate(result.data)
        
        completed++
        migrationProgress.value = Math.round((completed / total) * 100)
        migrationProgressText.value = `正在迁移 ${completed}/${total}`
        
        addLog(`成功迁移: ${result.templateName}`, 'success')
        
      } catch (error) {
        addLog(`迁移失败: ${result.templateName} - ${error.message}`, 'error')
      }
    }

    migrationStatus.value = 'success'
    migrationProgressText.value = `迁移完成 ${completed}/${total}`
    migrationCompleted.value = true
    
    migrationResult.value = {
      total,
      success: completed,
      failed: total - completed
    }
    
    addLog('模板迁移完成', 'success')
    
    // 自动进入下一步
    currentStep.value++

  } catch (error) {
    console.error('迁移失败:', error)
    migrationStatus.value = 'exception'
    addLog(`迁移失败: ${error.message}`, 'error')
  } finally {
    migrating.value = false
  }
}

const addLog = (message, type = 'info') => {
  migrationLogs.value.push({
    time: new Date(),
    message,
    type
  })
}

const downloadReport = () => {
  if (!migrationResult.value) return

  const report = generateMigrationReport(migrationResult.value)
  const blob = new Blob([report], { type: 'text/markdown' })
  const url = URL.createObjectURL(blob)
  
  const a = document.createElement('a')
  a.href = url
  a.download = `migration-report-${new Date().toISOString().split('T')[0]}.md`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  
  URL.revokeObjectURL(url)
}

const viewMigratedTemplates = () => {
  emit('migration-completed')
  handleClose()
}

const handleClose = () => {
  emit('update:visible', false)
  // 重置状态
  currentStep.value = 0
  selectedTemplates.value = []
  migrationPreview.value = null
  migrating.value = false
  migrationCompleted.value = false
  migrationLogs.value = []
  migrationResult.value = null
}

const formatDate = (dateStr) => {
  if (!dateStr) return '未知'
  return new Date(dateStr).toLocaleString('zh-CN')
}

const formatTime = (time) => {
  return time.toLocaleTimeString('zh-CN')
}

// 生命周期
onMounted(() => {
  if (props.visible) {
    loadLegacyTemplates()
  }
})
</script>

<style scoped>
.migration-wizard {
  .wizard-content {
    margin: 24px 0;
    min-height: 400px;
  }

  .template-selection {
    margin-top: 24px;
  }

  .template-selection h4 {
    margin: 0 0 16px 0;
  }

  .selection-info {
    margin-top: 16px;
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .preview-header h4 {
    margin: 0;
  }

  .stats {
    display: flex;
    flex-direction: column;
    gap: 4px;
    font-size: 12px;
  }

  .errors, .warnings {
    font-size: 12px;
  }

  .error-item {
    color: #f56c6c;
    margin-bottom: 4px;
  }

  .warning-item {
    color: #e6a23c;
    margin-bottom: 4px;
  }

  .migration-options {
    margin-bottom: 24px;
  }

  .migration-options h4 {
    margin: 0 0 16px 0;
  }

  .option-desc {
    margin-left: 8px;
    color: #666;
    font-size: 12px;
  }

  .migration-progress {
    text-align: center;
    margin-bottom: 24px;
  }

  .progress-text {
    margin-top: 8px;
    color: #666;
  }

  .migration-log h4 {
    margin: 0 0 16px 0;
  }

  .log-container {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    padding: 8px;
    background: #fafafa;
  }

  .log-item {
    display: flex;
    gap: 8px;
    margin-bottom: 4px;
    font-size: 13px;
  }

  .log-time {
    color: #999;
    min-width: 80px;
  }

  .log-item.success .log-message {
    color: #67c23a;
  }

  .log-item.error .log-message {
    color: #f56c6c;
  }

  .log-item.info .log-message {
    color: #409eff;
  }

  .result-stats {
    text-align: center;
  }

  .result-stats p {
    margin: 4px 0;
  }

  .wizard-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }
}
</style>
