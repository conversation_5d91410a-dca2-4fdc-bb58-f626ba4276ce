<template>
  <div class="batch-print-integration">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>批量打印集成</span>
          <el-button type="primary" @click="openBatchPrint">
            批量打印
          </el-button>
        </div>
      </template>

      <div class="integration-content">
        <el-row :gutter="16">
          <el-col :span="12">
            <div class="info-section">
              <h4>当前模板信息</h4>
              <el-descriptions :column="1" border>
                <el-descriptions-item label="模板名称">
                  {{ currentTemplate.templateName || '未命名模板' }}
                </el-descriptions-item>
                <el-descriptions-item label="模板类型">
                  {{ getTemplateTypeText(currentTemplate.templateType) }}
                </el-descriptions-item>
                <el-descriptions-item label="创建时间">
                  {{ formatDate(currentTemplate.createTime) }}
                </el-descriptions-item>
                <el-descriptions-item label="元素数量">
                  {{ elementCount }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-col>

          <el-col :span="12">
            <div class="preview-section">
              <h4>打印预览</h4>
              <div class="preview-container" id="batch-print-preview">
                <!-- 预览内容将在这里渲染 -->
              </div>
              <div class="preview-actions">
                <el-button @click="generatePreview">生成预览</el-button>
                <el-button type="primary" @click="testPrint">测试打印</el-button>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-divider />

        <div class="batch-settings">
          <h4>批量打印设置</h4>
          <el-form :model="batchSettings" label-width="120px">
            <el-row :gutter="16">
              <el-col :span="8">
                <el-form-item label="打印机">
                  <el-select v-model="batchSettings.printer" placeholder="选择打印机">
                    <el-option
                      v-for="printer in printerList"
                      :key="printer.name"
                      :label="printer.name"
                      :value="printer.name"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="打印份数">
                  <el-input-number
                    v-model="batchSettings.copies"
                    :min="1"
                    :max="10"
                    controls-position="right"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="打印质量">
                  <el-select v-model="batchSettings.quality">
                    <el-option label="草稿" value="draft" />
                    <el-option label="标准" value="normal" />
                    <el-option label="高质量" value="high" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <div class="data-mapping">
          <h4>数据字段映射</h4>
          <el-table :data="fieldMappings" border>
            <el-table-column prop="templateField" label="模板字段" />
            <el-table-column prop="dataField" label="数据字段" />
            <el-table-column prop="displayName" label="显示名称" />
            <el-table-column label="示例数据">
              <template #default="{ row }">
                <el-tag size="small">{{ row.sampleData }}</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>

    <!-- 批量打印对话框 -->
    <el-dialog
      v-model="batchPrintDialogVisible"
      title="批量打印"
      width="80%"
      :close-on-click-modal="false"
    >
      <BatchPrintDialog
        v-if="batchPrintDialogVisible"
        :template-data="templateData"
        :field-mappings="fieldMappings"
        @print-completed="handlePrintCompleted"
        @close="batchPrintDialogVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { hiprint } from 'vue-plugin-hiprint'
import BatchPrintDialog from './BatchPrintDialog.vue'
import { generatePrintData, getPrintPreview } from '../../batch-print/api'

// Props
const props = defineProps({
  hiprintTemplate: {
    type: Object,
    default: null
  },
  currentTemplate: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['print-completed'])

// 响应式数据
const batchPrintDialogVisible = ref(false)
const printerList = ref([])
const templateData = ref(null)

const batchSettings = reactive({
  printer: '',
  copies: 1,
  quality: 'normal'
})

const fieldMappings = ref([])

// 计算属性
const elementCount = computed(() => {
  if (!templateData.value || !templateData.value.panels) return 0
  return templateData.value.panels.reduce((count, panel) => {
    return count + (panel.printElements ? panel.printElements.length : 0)
  }, 0)
})

// 监听模板变化
watch(() => props.hiprintTemplate, (newTemplate) => {
  if (newTemplate) {
    updateTemplateData()
    updateFieldMappings()
  }
}, { immediate: true })

// 方法
const updateTemplateData = () => {
  if (props.hiprintTemplate) {
    try {
      templateData.value = props.hiprintTemplate.getJson()
      console.log('模板数据已更新:', templateData.value)
    } catch (error) {
      console.error('获取模板数据失败:', error)
    }
  }
}

const updateFieldMappings = () => {
  if (!templateData.value) return

  const mappings = []
  
  templateData.value.panels?.forEach(panel => {
    panel.printElements?.forEach(element => {
      if (element.options?.field) {
        mappings.push({
          templateField: element.options.field,
          dataField: element.options.field,
          displayName: getFieldDisplayName(element.options.field),
          sampleData: getSampleData(element.options.field)
        })
      }
    })
  })

  fieldMappings.value = mappings
}

const getFieldDisplayName = (fieldName) => {
  const fieldMap = {
    bankName: '银行名称',
    coinName1: '钱币名称1',
    coinName2: '钱币名称2',
    serialNumber: '编号',
    version: '版别',
    gradeScore: '评级分数',
    gradeLevel: '评级等级',
    specialMark: '特殊标记',
    diyCode: '送评条码',
    yearInfo: '年代信息',
    faceValue: '面值',
    coinType: '钱币类型'
  }
  return fieldMap[fieldName] || fieldName
}

const getSampleData = (fieldName) => {
  const sampleMap = {
    bankName: '中国人民银行',
    coinName1: '第四套人民币',
    coinName2: '1980年版',
    serialNumber: 'AB12345678',
    version: '普通版',
    gradeScore: '65',
    gradeLevel: 'EPQ',
    specialMark: '★',
    diyCode: 'DY2025001',
    yearInfo: '1980年',
    faceValue: '10元',
    coinType: '纸币'
  }
  return sampleMap[fieldName] || '示例数据'
}

const getTemplateTypeText = (type) => {
  const typeMap = {
    'CUSTOM': '自定义模板',
    'LARGE': '大签模板',
    'SMALL': '小签模板'
  }
  return typeMap[type] || '未知类型'
}

const formatDate = (dateStr) => {
  if (!dateStr) return '未知'
  return new Date(dateStr).toLocaleString('zh-CN')
}

const loadPrinterList = async () => {
  try {
    // 获取打印机列表
    if (props.hiprintTemplate && props.hiprintTemplate.getPrinterList) {
      const printers = await props.hiprintTemplate.getPrinterList()
      printerList.value = printers || []
    }
  } catch (error) {
    console.error('获取打印机列表失败:', error)
    ElMessage.warning('无法获取打印机列表，请确保打印客户端已连接')
  }
}

const generatePreview = async () => {
  if (!templateData.value) {
    ElMessage.warning('请先设计模板')
    return
  }

  try {
    // 使用示例数据生成预览
    const sampleData = {}
    fieldMappings.value.forEach(mapping => {
      sampleData[mapping.dataField] = mapping.sampleData
    })

    // 在预览容器中渲染
    const previewContainer = document.getElementById('batch-print-preview')
    if (previewContainer && props.hiprintTemplate) {
      props.hiprintTemplate.preview(previewContainer, sampleData)
    }
  } catch (error) {
    console.error('生成预览失败:', error)
    ElMessage.error('生成预览失败')
  }
}

const testPrint = () => {
  if (!templateData.value) {
    ElMessage.warning('请先设计模板')
    return
  }

  try {
    // 使用示例数据进行测试打印
    const sampleData = {}
    fieldMappings.value.forEach(mapping => {
      sampleData[mapping.dataField] = mapping.sampleData
    })

    if (props.hiprintTemplate) {
      props.hiprintTemplate.print(sampleData, {
        printer: batchSettings.printer,
        title: '标签测试打印'
      })
    }
  } catch (error) {
    console.error('测试打印失败:', error)
    ElMessage.error('测试打印失败')
  }
}

const openBatchPrint = () => {
  if (!templateData.value) {
    ElMessage.warning('请先设计并保存模板')
    return
  }

  if (fieldMappings.value.length === 0) {
    ElMessage.warning('模板中没有数据字段')
    return
  }

  batchPrintDialogVisible.value = true
}

const handlePrintCompleted = (result) => {
  console.log('批量打印完成:', result)
  ElMessage.success(`批量打印完成，共打印 ${result.count} 个标签`)
  emit('print-completed', result)
  batchPrintDialogVisible.value = false
}

// 生命周期
onMounted(() => {
  loadPrinterList()
})

// 暴露方法
defineExpose({
  updateTemplateData,
  generatePreview,
  testPrint
})
</script>

<style scoped>
.batch-print-integration {
  margin-top: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.integration-content {
  padding: 16px 0;
}

.info-section,
.preview-section {
  height: 100%;
}

.info-section h4,
.preview-section h4,
.batch-settings h4,
.data-mapping h4 {
  margin: 0 0 16px 0;
  color: #333;
  font-weight: 500;
}

.preview-container {
  min-height: 200px;
  border: 1px dashed #ddd;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  margin-bottom: 16px;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.batch-settings {
  margin: 24px 0;
}

.data-mapping {
  margin-top: 24px;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
}

:deep(.el-table) {
  margin-top: 8px;
}
</style>
