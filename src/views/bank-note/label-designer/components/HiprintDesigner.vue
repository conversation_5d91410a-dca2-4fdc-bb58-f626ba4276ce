<template>
  <div class="hiprint-designer">
    <!-- 顶部工具栏 -->
    <div class="designer-toolbar">
      <div class="toolbar-left">
        <el-button-group>
          <el-button type="primary" :icon="DocumentAdd" @click="newTemplate">
            新建模板
          </el-button>
          <el-button :icon="FolderOpened" @click="loadTemplate">
            加载模板
          </el-button>
          <el-button :icon="Document" @click="saveTemplate" :loading="saving">
            保存模板
          </el-button>
        </el-button-group>

        <el-divider direction="vertical" />

        <el-button-group>
          <el-button :icon="RefreshLeft" @click="undo" :disabled="!canUndo">
            撤销
          </el-button>
          <el-button :icon="RefreshRight" @click="redo" :disabled="!canRedo">
            重做
          </el-button>
        </el-button-group>

        <el-divider direction="vertical" />

        <el-button-group>
          <el-button :icon="View" @click="previewTemplate">
            预览
          </el-button>
          <el-button :icon="Printer" @click="printTemplate">
            打印
          </el-button>
        </el-button-group>
      </div>

      <div class="toolbar-right">
        <el-select v-model="currentTemplateType" @change="handleTemplateTypeChange" style="width: 120px">
          <el-option label="大签标签" value="large" />
          <el-option label="小签标签" value="small" />
        </el-select>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="designer-content">
      <!-- 左侧元素面板 -->
      <div class="elements-panel">
        <div class="panel-header">
          <span>设计元素</span>
        </div>
        <div class="panel-content">
          <!-- 基础元素 -->
          <div class="element-category">
            <h4>基础元素</h4>
            <div class="element-list">
              <div
                class="element-item ep-draggable-item"
                data-tid="defaultModule.text"
                @dragstart="handleElementDragStart"
              >
                <i class="el-icon-document"></i>
                <span>文本</span>
              </div>
              <div
                class="element-item ep-draggable-item"
                data-tid="defaultModule.longText"
                @dragstart="handleElementDragStart"
              >
                <i class="el-icon-document-copy"></i>
                <span>长文本</span>
              </div>
              <div
                class="element-item ep-draggable-item"
                data-tid="defaultModule.image"
                @dragstart="handleElementDragStart"
              >
                <i class="el-icon-picture"></i>
                <span>图片</span>
              </div>
            </div>
          </div>

          <!-- 条码元素 -->
          <div class="element-category">
            <h4>条码元素</h4>
            <div class="element-list">
              <div
                class="element-item ep-draggable-item"
                data-tid="defaultModule.text"
                data-type="barcode"
                @dragstart="handleElementDragStart"
              >
                <i class="el-icon-postcard"></i>
                <span>条形码</span>
              </div>
              <div
                class="element-item ep-draggable-item"
                data-tid="defaultModule.text"
                data-type="qrcode"
                @dragstart="handleElementDragStart"
              >
                <i class="el-icon-crop"></i>
                <span>二维码</span>
              </div>
            </div>
          </div>

          <!-- 钱币字段 -->
          <div class="element-category">
            <h4>钱币字段</h4>
            <div class="element-list">
              <div
                v-for="field in coinFields"
                :key="field.fieldName"
                class="element-item field-item"
                :data-field="field.fieldName"
                draggable="true"
                @dragstart="handleFieldDragStart($event, field)"
              >
                <i class="el-icon-coin"></i>
                <span>{{ field.displayName }}</span>
              </div>
            </div>
          </div>

          <!-- 评级字段 -->
          <div class="element-category">
            <h4>评级字段</h4>
            <div class="element-list">
              <div
                v-for="field in gradeFields"
                :key="field.fieldName"
                class="element-item field-item"
                :data-field="field.fieldName"
                draggable="true"
                @dragstart="handleFieldDragStart($event, field)"
              >
                <i class="el-icon-medal"></i>
                <span>{{ field.displayName }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间设计区域 -->
      <div class="design-area">
        <div class="design-header">
          <span>标签设计区域</span>
          <div class="design-controls">
            <el-button-group size="small">
              <el-button @click="zoomOut">缩小</el-button>
              <el-button>{{ Math.round(zoomLevel * 100) }}%</el-button>
              <el-button @click="zoomIn">放大</el-button>
            </el-button-group>
          </div>
        </div>

        <!-- hiprint 设计器容器 -->
        <div class="hiprint-container">
          <div id="hiprint-printTemplate" class="hiprint-design-area"></div>
          <!-- 分页容器 -->
          <div class="hiprint-printPagination"></div>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="properties-panel">
        <div class="panel-header">
          <span>属性设置</span>
        </div>
        <div class="panel-content">
          <!-- hiprint 属性设置容器 -->
          <div id="PrintElementOptionSetting"></div>

          <!-- 批量打印集成 -->
          <BatchPrintIntegration
            :hiprint-template="hiprintTemplate"
            :current-template="currentTemplate"
            @print-completed="handlePrintCompleted"
          />
        </div>
      </div>
    </div>

    <!-- 模板选择对话框 -->
    <el-dialog
      v-model="templateDialogVisible"
      title="选择模板"
      width="600px"
    >
      <el-table :data="templateList" @row-click="selectTemplate">
        <el-table-column prop="templateName" label="模板名称" />
        <el-table-column prop="templateType" label="模板类型" />
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button type="text" @click="deleteTemplate(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="标签预览"
      width="800px"
    >
      <div id="hiprint-preview-container"></div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  DocumentAdd,
  FolderOpened,
  Document,
  RefreshLeft,
  RefreshRight,
  View,
  Printer
} from '@element-plus/icons-vue'
import { hiprint, defaultElementTypeProvider } from 'vue-plugin-hiprint'
import {
  getFieldsByCategory,
  saveTemplate as saveTemplateApi,
  getTemplateList,
  deleteTemplate as deleteTemplateApi,
  previewLabel
} from '../api'
import {
  convertLabelZonesToHiprint,
  convertHiprintToLabelZones,
  createDefaultLargeTemplate,
  createDefaultSmallTemplate,
  FIELD_MAPPING,
  validateTemplateData
} from '../utils/dataAdapter'
import BatchPrintIntegration from './BatchPrintIntegration.vue'

// 响应式数据
const saving = ref(false)
const canUndo = ref(false)
const canRedo = ref(false)
const currentTemplateType = ref('large')
const zoomLevel = ref(1)
const templateDialogVisible = ref(false)
const previewDialogVisible = ref(false)
const templateList = ref([])
const coinFields = ref([])
const gradeFields = ref([])

// hiprint 实例
let hiprintTemplate = null

// 当前模板数据
const currentTemplate = reactive({
  id: null,
  templateName: '新建标签模板',
  templateType: 'CUSTOM',
  layoutConfig: '',
  fieldMapping: ''
})

// 初始化 hiprint
const initHiprint = async () => {
  try {
    // 禁用自动连接
    hiprint.disAutoConnect?.()
    
    // 初始化 hiprint
    hiprint.init({
      providers: [new defaultElementTypeProvider()],
      lang: 'cn'
    })

    // 构建可拖拽元素
    hiprint.PrintElementTypeManager.buildByHtml($('.ep-draggable-item'))

    // 创建模板实例
    hiprintTemplate = new hiprint.PrintTemplate({
      template: {},
      settingContainer: '#PrintElementOptionSetting',
      paginationContainer: '.hiprint-printPagination',
      dataMode: 1,
      history: true,
      onDataChanged: (type, json) => {
        console.log('模板数据变更:', type, json)
        updateUndoRedoState()
      },
      onUpdateError: (e) => {
        console.error('模板更新失败:', e)
        ElMessage.error('模板更新失败')
      }
    })

    // 设计器容器
    hiprintTemplate.design('#hiprint-printTemplate')

    // 设置默认画布尺寸
    setCanvasSize(currentTemplateType.value)

    console.log('hiprint 初始化完成')
  } catch (error) {
    console.error('hiprint 初始化失败:', error)
    ElMessage.error('设计器初始化失败')
  }
}

// 设置画布尺寸
const setCanvasSize = (templateType) => {
  if (!hiprintTemplate) return

  const sizes = {
    large: { width: 192, height: 26 }, // 大签尺寸 (mm)
    small: { width: 115, height: 21 }  // 小签尺寸 (mm)
  }

  const size = sizes[templateType] || sizes.large

  // 加载对应的默认模板
  const defaultTemplate = templateType === 'small'
    ? createDefaultSmallTemplate()
    : createDefaultLargeTemplate()

  try {
    hiprintTemplate.update(defaultTemplate)
    console.log('画布尺寸已设置:', size)
  } catch (error) {
    console.error('设置画布尺寸失败:', error)
  }
}

// 加载字段数据
const loadFields = async () => {
  try {
    const fieldCategories = await getFieldsByCategory()
    coinFields.value = fieldCategories.BASIC_INFO || FIELD_MAPPING.BASIC_INFO
    gradeFields.value = fieldCategories.GRADE_INFO || FIELD_MAPPING.GRADE_INFO
  } catch (error) {
    console.error('加载字段失败:', error)
    // 使用默认字段映射作为备用
    coinFields.value = FIELD_MAPPING.BASIC_INFO
    gradeFields.value = FIELD_MAPPING.GRADE_INFO
    ElMessage.warning('使用默认字段配置')
  }
}

// 事件处理
const handleElementDragStart = (event) => {
  // hiprint 会自动处理元素拖拽
}

const handleFieldDragStart = (event, field) => {
  // 设置拖拽数据
  event.dataTransfer.setData('application/json', JSON.stringify({
    type: 'field',
    field: field
  }))
}

const handleTemplateTypeChange = (templateType) => {
  currentTemplateType.value = templateType
  setCanvasSize(templateType)
}

// 工具栏操作
const newTemplate = () => {
  if (hiprintTemplate) {
    hiprintTemplate.clear()
    currentTemplate.id = null
    currentTemplate.templateName = '新建标签模板'
    ElMessage.success('已创建新模板')
  }
}

const loadTemplate = async () => {
  try {
    templateList.value = await getTemplateList()
    templateDialogVisible.value = true
  } catch (error) {
    console.error('加载模板列表失败:', error)
    ElMessage.error('加载模板列表失败')
  }
}

const selectTemplate = (template) => {
  if (hiprintTemplate && template.layoutConfig) {
    try {
      const layoutConfig = JSON.parse(template.layoutConfig)

      // 验证模板数据
      const validation = validateTemplateData(layoutConfig)
      if (!validation.valid) {
        ElMessage.error(`模板数据无效: ${validation.errors.join(', ')}`)
        return
      }

      hiprintTemplate.update(layoutConfig)

      Object.assign(currentTemplate, template)
      templateDialogVisible.value = false

      ElMessage.success('模板加载成功')
    } catch (error) {
      console.error('加载模板失败:', error)
      ElMessage.error('模板格式错误')
    }
  }
}

const saveTemplate = async () => {
  if (!hiprintTemplate) return

  try {
    const templateName = await ElMessageBox.prompt('请输入模板名称', '保存模板', {
      inputValue: currentTemplate.templateName,
      inputValidator: (value) => {
        if (!value || !value.trim()) {
          return '模板名称不能为空'
        }
        return true
      }
    })

    saving.value = true
    
    const templateData = hiprintTemplate.getJson()
    currentTemplate.templateName = templateName.value
    currentTemplate.layoutConfig = JSON.stringify(templateData)
    
    await saveTemplateApi(currentTemplate)
    ElMessage.success('模板保存成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('保存模板失败:', error)
      ElMessage.error('保存模板失败')
    }
  } finally {
    saving.value = false
  }
}

const undo = () => {
  if (hiprintTemplate && hiprintTemplate.undo) {
    hiprintTemplate.undo()
    updateUndoRedoState()
  }
}

const redo = () => {
  if (hiprintTemplate && hiprintTemplate.redo) {
    hiprintTemplate.redo()
    updateUndoRedoState()
  }
}

const updateUndoRedoState = () => {
  if (hiprintTemplate) {
    canUndo.value = hiprintTemplate.canUndo?.() || false
    canRedo.value = hiprintTemplate.canRedo?.() || false
  }
}

const previewTemplate = () => {
  if (hiprintTemplate) {
    // 实现预览功能
    previewDialogVisible.value = true
    nextTick(() => {
      hiprintTemplate.preview('#hiprint-preview-container')
    })
  }
}

const printTemplate = () => {
  if (hiprintTemplate) {
    hiprintTemplate.print({})
  }
}

const zoomIn = () => {
  zoomLevel.value = Math.min(zoomLevel.value + 0.1, 2)
  // 应用缩放
}

const zoomOut = () => {
  zoomLevel.value = Math.max(zoomLevel.value - 0.1, 0.5)
  // 应用缩放
}

const deleteTemplate = async (template) => {
  try {
    await ElMessageBox.confirm('确定要删除这个模板吗？', '确认删除', {
      type: 'warning'
    })

    await deleteTemplateApi(template.id)
    ElMessage.success('删除成功')

    // 重新加载模板列表
    templateList.value = await getTemplateList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除模板失败:', error)
      ElMessage.error('删除模板失败')
    }
  }
}

const handlePrintCompleted = (result) => {
  console.log('批量打印完成:', result)
  ElMessage.success(`批量打印完成，成功打印 ${result.completed} 个标签`)
}

// 生命周期
onMounted(async () => {
  await loadFields()
  await nextTick()
  await initHiprint()
})

onUnmounted(() => {
  if (hiprintTemplate) {
    hiprintTemplate.destroy?.()
  }
})
</script>

<style scoped>
.hiprint-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.designer-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.designer-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.elements-panel {
  width: 280px;
  background: white;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8;
  font-weight: 500;
  background: #fafafa;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.element-category {
  margin-bottom: 24px;
}

.element-category h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.element-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.element-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: grab;
  transition: all 0.2s;
  background: white;
}

.element-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.element-item:active {
  cursor: grabbing;
}

.element-item i {
  font-size: 20px;
  color: #409eff;
  margin-bottom: 4px;
}

.element-item span {
  font-size: 12px;
  color: #666;
}

.field-item {
  grid-column: 1 / -1;
}

.design-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

.design-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.hiprint-container {
  flex: 1;
  position: relative;
  overflow: auto;
}

.hiprint-design-area {
  min-height: 100%;
}

.properties-panel {
  width: 320px;
  background: white;
  border-left: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
}
</style>
